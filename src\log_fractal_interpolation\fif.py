"""Fractal interpolation functions (FIF).

This module implements routines for constructing and evaluating fractal 
interpolation functions following the mathematical formulation outlined 
in Guide.md.
"""

from __future__ import annotations
import math
from typing import Sequence, List, Tuple
from dataclasses import dataclass


@dataclass
class IFSParams:
    """Parameters for the Iterated Function System (IFS)."""
    a: List[float]  # horizontal scaling factors
    c: List[float]  # x coefficients
    e: List[float]  # horizontal translations
    f: List[float]  # y translations
    d: List[float]  # vertical scaling factors


def compute_ifs_params(xs: Sequence[float], ys: Sequence[float], d: Sequence[float]) -> IFSParams:
    """
    Compute IFS parameters from anchor points and vertical scaling factors.
    
    Args:
        xs: x-coordinates of anchor points [x0, ..., xN]
        ys: y-coordinates of anchor points [y0, ..., yN]  
        d: vertical scaling factors [d1, ..., dN] for intervals
    
    Returns:
        IFSParams containing coefficients for the fractal interpolation
    """
    xs, ys, d = list(xs), list(ys), list(d)
    N = len(xs) - 1
    
    if len(ys) != len(xs):
        raise ValueError("xs and ys must have same length")
    if len(d) != N:
        raise ValueError(f"d must have length {N} (number of intervals)")
    
    x0, xN = xs[0], xs[-1]
    denom = max(abs(xN - x0), 1e-12)
    
    # Horizontal transformation parameters (Eq. 15 from Guide.md)
    a = [(xs[i+1] - xs[i]) / denom for i in range(N)]
    e = [(xN * xs[i] - x0 * xs[i+1]) / denom for i in range(N)]
    
    # Vertical transformation parameters
    c = [(ys[i+1] - ys[i]) / denom - d[i] * (ys[-1] - ys[0]) / denom for i in range(N)]
    f = [(xN * ys[i] - x0 * ys[i+1]) / denom - d[i] * (xN * ys[0] - x0 * ys[-1]) / denom 
         for i in range(N)]
    
    return IFSParams(a=a, c=c, e=e, f=f, d=d)


def fif_eval_grid(xgrid: Sequence[float], xs: Sequence[float], ys: Sequence[float], 
                  d: Sequence[float], K: int = 8) -> List[float]:
    """
    Evaluate the FIF at points in xgrid using recursive functional equation.
    
    Args:
        xgrid: evaluation points
        xs: x-coordinates of anchor points
        ys: y-coordinates of anchor points
        d: vertical scaling factors
        K: recursion depth (6-10 typically sufficient for x4 SR)
    
    Returns:
        FIF values at xgrid points
    """
    params = compute_ifs_params(xs, ys, d)
    xs = list(xs)
    
    def find_interval(x: float) -> int:
        """Find interval n such that x is in [x_n, x_{n+1}]"""
        for i in range(len(xs) - 1):
            if xs[i] <= x <= xs[i+1]:
                return i
        # Handle edge cases
        if x < xs[0]:
            return 0
        return len(xs) - 2
    
    def recurse_at_x(x: float, k: int) -> float:
        """Recursive evaluation of FIF functional equation"""
        n = find_interval(x)
        an, cn, en, fn, dn = params.a[n], params.c[n], params.e[n], params.f[n], params.d[n]
        
        # Inverse transformation: u = L_n^{-1}(x)
        u = (x - en) / max(abs(an), 1e-12)
        
        if k == 0:
            # Base case: linear interpolation
            m = find_interval(u)
            if m >= len(xs) - 1:
                m = len(xs) - 2
            xL, xR = xs[m], xs[m+1]
            yL, yR = ys[m], ys[m+1]
            w = (u - xL) / max(abs(xR - xL), 1e-12)
            w = max(0.0, min(1.0, w))  # clamp to [0,1]
            fu = (1 - w) * yL + w * yR
        else:
            # Recursive case
            fu = recurse_at_x(u, k - 1)
        
        return cn * u + dn * fu + fn
    
    return [recurse_at_x(float(x), K) for x in xgrid]


def fractal_dimension_from_ad(a: Sequence[float], d: Sequence[float], 
                             lo: float = 1.0, hi: float = 2.0, 
                             tol: float = 1e-6, maxit: int = 60) -> float:
    """
    Solve for fractal dimension D in: sum |d_i| |a_i|^{D-1} = 1
    using bisection method on interval [1, 2].
    
    Args:
        a: horizontal scaling factors
        d: vertical scaling factors
        lo: lower bound for search
        hi: upper bound for search
        tol: tolerance for convergence
        maxit: maximum iterations
    
    Returns:
        Fractal dimension D, or NaN if no solution found
    """
    a_abs = [abs(x) for x in a]
    d_abs = [abs(x) for x in d]
    
    def g(D: float) -> float:
        """Function whose zero gives the fractal dimension"""
        return sum(d_abs[i] * (a_abs[i] ** (D - 1)) for i in range(len(a_abs))) - 1.0
    
    gl, gh = g(lo), g(hi)
    if gl * gh > 0:
        return float('nan')  # No sign change, no solution
    
    l, h = lo, hi
    for _ in range(maxit):
        m = 0.5 * (l + h)
        gm = g(m)
        if abs(gm) < tol:
            return m
        if gl * gm <= 0:
            h, gh = m, gm
        else:
            l, gl = m, gm
    
    return 0.5 * (l + h)


def hurst_rs(x: Sequence[float], window_sizes: Sequence[int] = None) -> float:
    """
    Estimate Hurst exponent using R/S analysis.
    
    Args:
        x: time series data
        window_sizes: sizes of windows for R/S calculation
    
    Returns:
        Hurst exponent H (0 < H < 1)
    """
    x = list(x)
    n = len(x)
    
    if window_sizes is None:
        # Log-spaced window sizes
        min_size = max(16, int(n ** 0.25))
        max_size = max(32, n // 4)
        num_windows = min(8, int(math.log2(max_size / min_size)) + 1)
        window_sizes = [int(min_size * (max_size / min_size) ** (i / (num_windows - 1))) 
                       for i in range(num_windows)]
        window_sizes = sorted(list(set(window_sizes)))  # remove duplicates
    
    log_rs, log_T = [], []
    
    for T in window_sizes:
        if T < 16 or T > n:
            continue
        
        nseg = n // T
        if nseg < 1:
            continue
            
        rs_vals = []
        for s in range(nseg):
            seg = x[s*T:(s+1)*T]
            if len(seg) < T:
                continue
                
            mean_seg = sum(seg) / len(seg)
            
            # Cumulative sum of deviations from mean
            y = [0.0]
            for val in seg:
                y.append(y[-1] + (val - mean_seg))
            y = y[1:]  # remove initial 0
            
            # Range and standard deviation
            R = max(y) - min(y)
            if len(seg) > 1:
                variance = sum((val - mean_seg)**2 for val in seg) / (len(seg) - 1)
                S = math.sqrt(variance) + 1e-12
            else:
                S = 1e-12
                
            rs_vals.append(R / S)
        
        if len(rs_vals) == 0:
            continue
            
        mean_rs = sum(rs_vals) / len(rs_vals)
        if mean_rs > 0:
            log_rs.append(math.log(mean_rs))
            log_T.append(math.log(T))
    
    if len(log_T) < 2:
        return 0.5  # fallback
    
    # Linear regression to find slope (Hurst exponent)
    n_points = len(log_T)
    sum_x = sum(log_T)
    sum_y = sum(log_rs)
    sum_xx = sum(x*x for x in log_T)
    sum_xy = sum(log_T[i] * log_rs[i] for i in range(n_points))
    
    denom = n_points * sum_xx - sum_x * sum_x
    if abs(denom) < 1e-12:
        return 0.5
        
    slope = (n_points * sum_xy - sum_x * sum_y) / denom
    return max(0.1, min(0.9, slope))  # clamp to reasonable range


def placeholder_fif(x: Sequence[float]) -> List[float]:
    """Placeholder implementation returning the input unchanged."""
    return list(x)

