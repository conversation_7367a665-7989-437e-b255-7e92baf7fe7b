import sys
import math
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))

from log_fractal_interpolation import (
    DummyBiLSTM,
    BiLSTMConfig,
    train_bilstm_on_fif,
    bilstm_infer,
)


def test_bilstm_config():
    """Test BiLSTM configuration."""
    config = BiLSTMConfig()
    
    # Test default values
    assert config.window_size == 21
    assert config.hidden_size == 32
    assert config.learning_rate == 0.01
    assert config.epochs == 50
    assert config.batch_size == 32
    
    # Test custom values
    custom_config = BiLSTMConfig(window_size=15, hidden_size=64)
    assert custom_config.window_size == 15
    assert custom_config.hidden_size == 64


def test_dummy_bilstm_initialization():
    """Test DummyBiLSTM initialization."""
    model = DummyBiLSTM()
    
    assert isinstance(model, DummyBiLSTM)
    assert not model.is_trained
    assert model.config.window_size == 21
    
    # Test with custom config
    config = BiLSTMConfig(window_size=11, hidden_size=16)
    model = DummyBiLSTM(config=config)
    assert model.config.window_size == 11
    assert model.config.hidden_size == 16


def test_dummy_bilstm_predict_single():
    """Test single prediction."""
    model = DummyBiLSTM(seed=42)
    
    # Create a simple window
    window = [[[0.1]], [[0.2]], [[0.3]], [[0.4]], [[0.5]]]  # [window_size, 1]
    
    result = model.predict_single(window)
    
    assert isinstance(result, float)
    assert not math.isnan(result)
    assert not math.isinf(result)


def test_dummy_bilstm_predict_batch():
    """Test batch prediction."""
    model = DummyBiLSTM(seed=123)
    
    # Create batch of windows
    window1 = [[[0.1]], [[0.2]], [[0.3]]]
    window2 = [[[0.4]], [[0.5]], [[0.6]]]
    window3 = [[[0.7]], [[0.8]], [[0.9]]]
    
    X = [window1, window2, window3]
    
    results = model.predict(X)
    
    assert len(results) == 3
    assert all(isinstance(r, float) for r in results)
    assert all(not math.isnan(r) for r in results)


def test_dummy_bilstm_training():
    """Test BiLSTM training process."""
    config = BiLSTMConfig(window_size=5, epochs=10, hidden_size=8)
    model = DummyBiLSTM(config=config, seed=42)
    
    # Create simple training data
    X = [
        [[[0.1]], [[0.2]], [[0.3]], [[0.4]], [[0.5]]],
        [[[0.2]], [[0.3]], [[0.4]], [[0.5]], [[0.6]]],
        [[[0.3]], [[0.4]], [[0.5]], [[0.6]], [[0.7]]],
    ]
    y = [0.3, 0.4, 0.5]  # Target values
    
    # Train model
    metrics = model.train(X, y)
    
    # Check training completed
    assert model.is_trained
    assert isinstance(metrics, dict)
    assert 'initial_loss' in metrics
    assert 'final_loss' in metrics
    assert 'improvement' in metrics
    
    # Check metrics are reasonable
    assert metrics['initial_loss'] >= 0
    assert metrics['final_loss'] >= 0
    assert isinstance(metrics['improvement'], float)


def test_train_bilstm_on_fif():
    """Test end-to-end training function."""
    # Simple FIF sequence
    fif_seq = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    y_hr = [0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95]
    
    config = BiLSTMConfig(window_size=5, epochs=5, hidden_size=4)
    
    model, metrics = train_bilstm_on_fif(fif_seq, y_hr, config)
    
    # Check model was returned and trained
    assert isinstance(model, DummyBiLSTM)
    assert model.is_trained
    
    # Check metrics
    assert isinstance(metrics, dict)
    assert 'initial_loss' in metrics


def test_bilstm_infer():
    """Test BiLSTM inference function."""
    # Create and train a simple model
    fif_seq = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6]
    y_hr = [0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65]
    
    config = BiLSTMConfig(window_size=3, epochs=3)
    model, _ = train_bilstm_on_fif(fif_seq, y_hr, config)
    
    # Test inference
    test_seq = [0.1, 0.2, 0.3, 0.4, 0.5]
    predictions = bilstm_infer(model, test_seq)
    
    assert len(predictions) == len(test_seq)
    assert all(isinstance(p, float) for p in predictions)
    assert all(not math.isnan(p) for p in predictions)


def test_bilstm_consistency():
    """Test that BiLSTM gives consistent results with same seed."""
    fif_seq = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    config = BiLSTMConfig(window_size=3)
    
    # Create two models with same seed
    model1 = DummyBiLSTM(config=config, seed=42)
    model2 = DummyBiLSTM(config=config, seed=42)
    
    # Same input
    window = [[[0.1]], [[0.2]], [[0.3]]]
    
    pred1 = model1.predict_single(window)
    pred2 = model2.predict_single(window)
    
    # Should be identical (same seed)
    assert abs(pred1 - pred2) < 1e-10


def test_error_handling():
    """Test error handling in BiLSTM functions."""
    # Mismatched lengths
    try:
        train_bilstm_on_fif([1, 2, 3], [1, 2])  # Different lengths
        assert False, "Should raise ValueError"
    except ValueError:
        pass
    
    # Empty sequences
    try:
        train_bilstm_on_fif([], [])
        assert False, "Should raise ValueError or handle gracefully"
    except (ValueError, IndexError):
        pass


def test_edge_cases():
    """Test edge cases for BiLSTM."""
    model = DummyBiLSTM()
    
    # Empty window
    result = model.predict_single([])
    assert result == 0.0
    
    # Single element window
    result = model.predict_single([[[0.5]]])
    assert isinstance(result, float)
    
    # Very small training set
    X = [[[[0.1]], [[0.2]], [[0.3]]]]
    y = [0.2]
    
    metrics = model.train(X, y)
    assert isinstance(metrics, dict)


if __name__ == "__main__":
    test_bilstm_config()
    test_dummy_bilstm_initialization()
    test_dummy_bilstm_predict_single()
    test_dummy_bilstm_predict_batch()
    test_dummy_bilstm_training()
    test_train_bilstm_on_fif()
    test_bilstm_infer()
    test_bilstm_consistency()
    test_error_handling()
    test_edge_cases()
    print("All BiLSTM tests passed!")