```markdown
### Theoretical background and replication guideline

#### Scope
This document summarizes the key theory and a practical replication guideline for “Well Logging Super‑Resolution Based on Fractal Interpolation Enhanced by BiLSTM‑AMPSO.” It is tailored for well‑log curves such as GR, DEN, NPHI, etc., sampled along depth.

#### 1) Fractal characteristics of well logs
Well‑logging curves exhibit scale‑invariant, self‑affine behavior. Two related quantities are commonly used:
- Hurst exponent H (0 < H < 1) estimated from R/S analysis. Persistence increases as H approaches 1; anti‑persistence near 0.
- Fractal dimension D of a self‑affine 1D curve relates to H via D = 2 − H (idealized). Larger D indicates rougher curves.

Practical notes: compute H on sufficiently long segments; downsampling can bias H upward; use multiple window sizes in log–log R/S fits and avoid very small windows.

#### 2) Fractal interpolation function (FIF)
Given LR anchor points (x_i, y_i), i = 0..N, an iterated function system (IFS) constructs a self‑affine interpolant that exactly passes through each anchor while adding fractal detail controlled by vertical scaling factors d_n (n = 1..N). For each interval [x_{n-1}, x_n], define affine maps:
- Horizontal: L_n(x) = a_n x + e_n, mapping global domain [x_0, x_N] to local interval.
- Vertical: F_n(x, y) = c_n x + d_n y + f_n, with |d_n| < 1 for contractivity.
Coefficients (a_n, c_n, e_n, f_n) are determined by boundary constraints to enforce interpolation at anchors.

The FIF at a query x is defined implicitly by the functional equation on the interval containing x:
  f(x) = c_n u + d_n f(u) + f_n,  where u = L_n^{-1}(x)
which is evaluated via finite‑depth recursion (K ~ 6–10 for ×4 SR).

An approximate fractal dimension D_f of the FIF satisfies the Moran‑type equation:
  sum_{n=1}^N |d_n| |a_n|^{D_f − 1} = 1, with 1 ≤ D_f ≤ 2

#### 3) AMPSO optimization of vertical scaling factors
We choose d = {d_n} to balance fidelity and fractal plausibility. Adaptive mutation PSO (AMPSO) augments PSO with a nonlinear inertia schedule and stochastic mutation of positions. Objective:
  J(d) = MSE(y_HR, FIF_seed(d)) + μ (D_f(d) − D_target)^2 + λ_smooth ||Δd||^2
- If HR is unavailable (inference), drop the MSE term and rely on the fractal‑dimension penalty and smoothness.
- D_target = 2 − H, with H from the observed curve.
- Typical hyperparameters: c1 = c2 = 2; inertia decreases from ~0.9 to ~0.4; mutation probability ≈ 0.7; |d_n| < 1.

#### 4) BiLSTM refinement of FIF seed
The fractal interpolant provides a physics‑inspired, structure‑preserving seed. A BiLSTM learns local mappings from FIF neighborhoods to true HR samples. Inputs are sliding windows (e.g., 21 points) centered at each HR depth; the model’s output is the center point. A simple head (ReLU MLP) maps the concatenated forward/backward hidden states to a scalar.

Training uses MSE; validation monitors early stopping. The trained network generalizes across wells within the same geological trend and normalization.

#### 5) Data preprocessing and normalization
- Remove or inpaint isolated NaNs; keep the depth grid strictly monotonic.
- Normalize curves to [0,1] using min–max on the training well; reuse these stats for inference to enable PSNR comparison.
- Downsample HR to LR anchors by an integer factor m (e.g., 4) to simulate wireline resolution loss during training.

#### 6) Evaluation
Report RMSE, MAE, R², Pearson correlation (PCC), and PSNR on normalized scale. For spectral analysis, compare |FFT| magnitudes to verify high‑frequency recovery. Include baselines such as linear/cubic interpolation, SRCNN, and random forest, if available.

#### 7) End‑to‑end recipe
- Training with HR: normalize, compute H, set D_target, optimize d via AMPSO (with MSE + penalties), render FIF seed at HR grid, train BiLSTM on windows of the seed to predict HR. Validate and tune μ, window size, K, and PSO settings.
- Inference with only LR: normalize using training stats, compute H and D_target from LR, re‑optimize d with penalties only (no HR), render FIF seed on the desired HR grid, and run the trained BiLSTM to obtain SR. Optionally add smoothness penalty on d.

References: Barnsley’s fractal interpolation and standard PSO/AMPSO literature; the target paper for bi‑directional LSTM refinement and practical hyperparameters.
``````markdown
### Python implementation: FIF + AMPSO + BiLSTM for well-log super-resolution

This document provides a self-contained Python implementation you can paste into a notebook or module. It follows the guideline.md and the target paper.

```python
# Core libraries
import numpy as np
from dataclasses import dataclass
from typing import Tuple, Optional, Dict

# SciPy / sklearn
from scipy.interpolate import interp1d
from scipy.stats import pearsonr
from sklearn.model_selection import train_test_split

# Torch for BiLSTM
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader

# -----------------------------
# 0) Utilities: normalization, downsampling, windows
# -----------------------------

@dataclass
class NormStats:
    xmin: float
    xmax: float

def normalize_01(y: np.ndarray) -> Tuple[np.ndarray, NormStats]:
    xmin = np.nanmin(y)
    xmax = np.nanmax(y)
    denom = max(xmax - xmin, 1e-12)
    y_norm = (y - xmin) / denom
    return y_norm, NormStats(xmin, xmax)

def denormalize_01(y_norm: np.ndarray, stats: NormStats) -> np.ndarray:
    return y_norm * (stats.xmax - stats.xmin) + stats.xmin

def downsample_uniform(x: np.ndarray, y: np.ndarray, factor: int) -> Tuple[np.ndarray, np.ndarray]:
    assert factor >= 2
    return x[::factor], y[::factor]

def make_windows_1d(seq: np.ndarray, win: int) -> np.ndarray:
    assert win % 2 == 1, "Window size must be odd"
    pad = win // 2
    s = np.pad(seq, (pad, pad), mode='edge')
    X = np.stack([s[i:i+win] for i in range(len(seq))], axis=0)  # [N, W]
    return X[..., None]  # [N, W, 1]

# -----------------------------
# 1) Hurst exponent via R/S analysis
# -----------------------------

def hurst_rs(x: np.ndarray, window_sizes: Optional[np.ndarray]=None) -> float:
    x = np.asarray(x, dtype=float)
    n = len(x)
    if window_sizes is None:
        # log-spaced window sizes
        ws = np.unique((np.logspace(np.log10(16), np.log10(max(32, n//4)), num=8)).astype(int))
    else:
        ws = np.asarray(window_sizes, dtype=int)
    log_rs, log_T = [], []
    for T in ws:
        if T < 16 or T > n: 
            continue
        nseg = n // T
        if nseg < 1:
            continue
        rs_vals = []
        for s in range(nseg):
            seg = x[s*T:(s+1)*T]
            mean = seg.mean()
            y = np.cumsum(seg - mean)
            R = y.max() - y.min()
            S = seg.std(ddof=1) + 1e-12
            rs_vals.append(R / S)
        if len(rs_vals) == 0:
            continue
        log_rs.append(np.log(np.mean(rs_vals)))
        log_T.append(np.log(T))
    if len(log_T) < 2:
        # fallback if series is too short
        return 0.5
    slope, _ = np.polyfit(log_T, log_rs, 1)
    return float(slope)

# -----------------------------
# 2) Fractal Interpolation Function (FIF) building blocks
# -----------------------------

def compute_ifs_params(xs: np.ndarray, ys: np.ndarray, d: np.ndarray):
    """
    xs: [x0,...,xN], ys: [y0,...,yN] (anchors at LR positions), d: [d1,...,dN] vertical scaling
    Returns (a, c, e, f) for n=1..N with length N arrays.
    """
    xs, ys, d = map(np.asarray, (xs, ys, d))
    assert len(xs) == len(ys), "xs and ys must be same length"
    N = len(xs) - 1
    assert len(d) == N, "d must have length N (number of intervals)"
    x0, xN = xs[0], xs[-1]
    denom = (xN - x0) + 1e-12
    a = (xs[1:] - xs[:-1]) / denom
    e = (xN*xs[:-1] - x0*xs[1:]) / denom
    # Eq. (15)
    c = (ys[1:] - ys[:-1]) / denom - d * (ys[-1] - ys[0]) / denom
    f = (xN*ys[:-1] - x0*ys[1:]) / denom - d * (xN*ys[0] - x0*ys[-1]) / denom
    return a, c, e, f

def fif_eval_grid(xgrid: np.ndarray, xs: np.ndarray, ys: np.ndarray, d: np.ndarray, K: int=8) -> np.ndarray:
    """
    Evaluate the FIF at points xgrid by recursive functional equation.
    K: recursion depth (6-10 typically sufficient for x4 SR).
    """
    a, c, e, f = compute_ifs_params(xs, ys, d)
    xs = np.asarray(xs)
    xgrid = np.asarray(xgrid)

    def recurse_at_x(x: float, k: int) -> float:
        # find interval n so that x in [x_{n}, x_{n+1}]
        n = int(np.clip(np.searchsorted(xs, x, side='right') - 1, 0, len(xs)-2))
        an, cn, en, fn, dn = a[n], c[n], e[n], f[n], d[n]
        u = (x - en) / (an + 1e-12)  # inverse of L_n(x) = a_n x + e_n
        if k == 0:
            # base: linear interpolation around u on its interval
            m = int(np.clip(np.searchsorted(xs, u, side='right') - 1, 0, len(xs)-2))
            xL, xR = xs[m], xs[m+1]
            yL, yR = ys[m], ys[m+1]
            w = (u - xL) / (xR - xL + 1e-12)
            fu = (1-w)*yL + w*yR
            return cn*u + dn*fu + fn
        else:
            fu = recurse_at_x(u, k-1)
            return cn*u + dn*fu + fn

    return np.array([recurse_at_x(float(x), K) for x in xgrid])

def fractal_dimension_from_ad(a: np.ndarray, d: np.ndarray, lo=1.0, hi=2.0, tol=1e-6, maxit=60) -> float:
    """
    Solve for D in sum |d_i| |a_i|^{D-1} = 1 by bisection on [1,2].
    """
    a, d = np.abs(a), np.abs(d)
    def g(D):
        return (d * (a**(D-1))).sum() - 1.0
    gl, gh = g(lo), g(hi)
    if gl*gh > 0:
        return np.nan  # no sign change
    l, h = lo, hi
    for _ in range(maxit):
        m = 0.5*(l+h)
        gm = g(m)
        if abs(gm) < tol: return m
        if gl*gm <= 0: h, gh = m, gm
        else: l, gl = m, gm
    return 0.5*(l+h)

# -----------------------------
# 3) AMPSO optimizer for vertical scaling factors d_n
# -----------------------------

@dataclass
class AMPSOConfig:
    swarm_size: int = 40
    iters: int = 200
    c1: float = 2.0
    c2: float = 2.0
    w_start: float = 0.9
    w_end: float = 0.4
    p_mut: float = 0.7
    gamma: float = 1.0
    d_clip: float = 0.95  # ensure |d| < 1

def inertia_nonlinear(t: int, T: int, w_start=0.9, w_end=0.4) -> float:
    # Eq. (26)
    return w_start - (w_start - w_end)*(1.0 - np.exp(-15.0*((t/max(T,1.0))**4)))

def fitness_dn(
    d: np.ndarray,
    xs: np.ndarray, ys_anchor: np.ndarray, x_hr: np.ndarray, y_hr: Optional[np.ndarray],
    D_target: float, mu: float, K: int=8, lam_smooth: float=0.0
) -> float:
    """
    Objective: MSE(HR, FIF_seed) + mu*(Df - D_target)^2 + lam_smooth*||Δd||^2
    If y_hr is None (inference without HR), drop MSE and rely on penalties.
    """
    a, c, e, f = compute_ifs_params(xs, ys_anchor, d)
    Df = fractal_dimension_from_ad(a, d)
    dim_pen = 0.0 if np.isnan(Df) else (Df - D_target)**2
    smooth_pen = 0.0
    if lam_smooth > 0 and len(d) > 1:
        dd = np.diff(d)
        smooth_pen = np.mean(dd*dd)
    mse = 0.0
    if y_hr is not None:
        y_seed = fif_eval_grid(x_hr, xs, ys_anchor, d, K=K)
        mse = float(np.mean((y_hr - y_seed)**2))
    return mse + mu*dim_pen + lam_smooth*smooth_pen

def ampspso_optimize_dn(
    xs: np.ndarray, ys_anchor: np.ndarray,
    x_hr: np.ndarray, y_hr: Optional[np.ndarray],
    D_target: float, mu: float=1e-2,
    cfg: AMPSOConfig = AMPSOConfig(),
    K: int=8, lam_smooth: float=0.0,
    seed: int=42
) -> np.ndarray:
    """
    Optimize d in [-d_clip, d_clip]^{N} using AMPSO with mutation.
    xs, ys_anchor: LR anchor positions/values (length N+1).
    x_hr, y_hr: HR grid and targets. If y_hr is None, do unsupervised (penalty-based).
    """
    rng = np.random.default_rng(seed)
    Nint = len(xs) - 1
    low, high = -cfg.d_clip, cfg.d_clip

    # Initialize swarm
    X = rng.uniform(low, high, size=(cfg.swarm_size, Nint))
    V = np.zeros_like(X)
    pbest = X.copy()
    def fit_row(d):
        return fitness_dn(d, xs, ys_anchor, x_hr, y_hr, D_target, mu, K=K, lam_smooth=lam_smooth)
    pbest_fit = np.array([fit_row(row) for row in X])
    gidx = int(np.argmin(pbest_fit))
    gbest = pbest[gidx].copy()
    gbest_fit = pbest_fit[gidx]

    for t in range(1, cfg.iters+1):
        w = inertia_nonlinear(t, cfg.iters, cfg.w_start, cfg.w_end)
        r1 = rng.random((cfg.swarm_size, Nint))
        r2 = rng.random((cfg.swarm_size, Nint))
        # Velocity and position update
        V = (w*V 
             + cfg.c1*r1*(pbest - X)
             + cfg.c2*r2*(gbest[None,:] - X))
        X = X + cfg.gamma*V

        # Mutation (Eq. 27)
        mut_mask = rng.random((cfg.swarm_size, Nint)) < cfg.p_mut
        X = np.where(mut_mask, (2*rng.random(X.shape)-1)/5.0, X)  # [-0.2, 0.2]
        # Clip to feasible range
        X = np.clip(X, low, high)

        # Evaluate
        fits = np.array([fit_row(row) for row in X])
        improved = fits < pbest_fit
        pbest[improved] = X[improved]
        pbest_fit[improved] = fits[improved]
        if pbest_fit.min() < gbest_fit:
            gidx = int(np.argmin(pbest_fit))
            gbest = pbest[gidx].copy()
            gbest_fit = pbest_fit[gidx]
    return gbest

# -----------------------------
# 4) BiLSTM model + training
# -----------------------------

class BiLSTMSR(nn.Module):
    def __init__(self, in_dim=1, hidden=200):
        super().__init__()
        self.lstm = nn.LSTM(input_size=in_dim, hidden_size=hidden, num_layers=1,
                            bidirectional=True, batch_first=True)
        self.head = nn.Sequential(
            nn.Linear(2*hidden, 128), nn.ReLU(),
            nn.Linear(128, 1)
        )

    def forward(self, x):  # x: [B, W, 1]
        y,_ = self.lstm(x)
        mid = y[:, y.shape[1]//2, :]  # [B, 2H]
        out = self.head(mid)
        return out.squeeze(-1)

class SeqDataset(Dataset):
    def __init__(self, X: np.ndarray, y: np.ndarray):
        self.X = torch.from_numpy(X.astype(np.float32))
        self.y = torch.from_numpy(y.astype(np.float32))
    def __len__(self): return len(self.X)
    def __getitem__(self, i): return self.X[i], self.y[i]

@dataclass
class TrainConfig:
    win: int = 21
    batch_size: int = 256
    epochs: int = 40
    lr: float = 1e-3
    val_size: float = 0.2
    seed: int = 123

def train_bilstm_on_fif(
    fif_seq: np.ndarray, y_hr: np.ndarray, cfg: TrainConfig = TrainConfig(),
    device: Optional[str]=None
) -> Tuple[BiLSTMSR, Dict[str,float]]:
    assert len(fif_seq) == len(y_hr)
    X = make_windows_1d(fif_seq, cfg.win)
    y = y_hr.copy()
    Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=cfg.val_size, random_state=cfg.seed)
    dtr, dval = SeqDataset(Xtr, ytr), SeqDataset(Xval, yval)
    tr_loader = DataLoader(dtr, batch_size=cfg.batch_size, shuffle=True)
    val_loader = DataLoader(dval, batch_size=cfg.batch_size, shuffle=False)

    dev = device or ("cuda" if torch.cuda.is_available() else "cpu")
    model = BiLSTMSR(in_dim=1, hidden=200).to(dev)
    opt = torch.optim.Adam(model.parameters(), lr=cfg.lr)
    crit = nn.MSELoss()

    best_state = None
    best_val = np.inf
    patience, no_improve = 8, 0

    for epoch in range(cfg.epochs):
        model.train()
        tr_loss = 0.0
        for xb, yb in tr_loader:
            xb, yb = xb.to(dev), yb.to(dev)
            opt.zero_grad()
            pred = model(xb)
            loss = crit(pred, yb)
            loss.backward()
            opt.step()
            tr_loss += loss.item()*len(xb)
        tr_loss /= len(dtr)

        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for xb, yb in val_loader:
                xb, yb = xb.to(dev), yb.to(dev)
                pred = model(xb)
                loss = crit(pred, yb)
                val_loss += loss.item()*len(xb)
        val_loss /= len(dval)

        if val_loss < best_val - 1e-6:
            best_val = val_loss
            best_state = {k:v.cpu().clone() for k,v in model.state_dict().items()}
            no_improve = 0
        else:
            no_improve += 1
            if no_improve >= patience:
                break

    if best_state is not None:
        model.load_state_dict(best_state)
    return model, {"val_mse": float(best_val)}

def bilstm_infer(model: BiLSTMSR, fif_seq: np.ndarray, win: int=21, device: Optional[str]=None) -> np.ndarray:
    X = make_windows_1d(fif_seq, win)
    dev = device or ("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(dev).eval()
    yhat = []
    with torch.no_grad():
        for i in range(0, len(X), 1024):
            xb = torch.from_numpy(X[i:i+1024].astype(np.float32)).to(dev)
            yb = model(xb).cpu().numpy()
            yhat.append(yb)
    return np.concatenate(yhat, axis=0)

# -----------------------------
# 5) Metrics and FFT helper
# -----------------------------

def compute_metrics(y: np.ndarray, yhat: np.ndarray) -> Dict[str, float]:
    y = np.asarray(y); yhat = np.asarray(yhat)
    rmse = np.sqrt(np.mean((y - yhat)**2))
    mae = np.mean(np.abs(y - yhat))
    ss_res = np.sum((y - yhat)**2)
    ss_tot = np.sum((y - y.mean())**2) + 1e-12
    r2 = 1 - ss_res/ss_tot
    pcc = pearsonr(y, yhat)[0]
    mse = np.mean((y - yhat)**2) + 1e-12
    psnr = 10*np.log10(1.0/mse)  # normalized range [0,1]
    return dict(RMSE=float(rmse), MAE=float(mae), R2=float(r2), PCC=float(pcc), PSNR=float(psnr))

def fft_magnitude(y: np.ndarray) -> np.ndarray:
    y = np.asarray(y) - np.mean(y)
    Y = np.fft.rfft(y)
    return np.abs(Y)

# -----------------------------
# 6) End-to-end training & inference pipeline
# -----------------------------

@dataclass
class SRModel:
    xs_lr: np.ndarray
    ys_anchor: np.ndarray
    dn: np.ndarray
    bilstm: BiLSTMSR
    norm_stats: NormStats
    win: int
    K: int

def train_sr_model_on_well(
    depth_hr: np.ndarray, curve_hr: np.ndarray,
    factor: int = 4,
    mu: float = 1e-2,
    lam_smooth: float = 0.0,
    K: int = 8,
    pso_cfg: AMPSOConfig = AMPSOConfig(),
    train_cfg: TrainConfig = TrainConfig(),
    seed: int = 1234
) -> Tuple[SRModel, Dict[str, float]]:
    """
    Train the AMPSO+FIF+BiLSTM SR model on one well with HR ground truth.
    Returns the trained model and performance metrics on the training well (for verification).
    """
    # 1) Clean/normalize
    y_norm, norm_stats = normalize_01(curve_hr)
    x_hr = depth_hr.astype(float)

    # 2) Downsample to LR anchors
    xs_lr, ys_lr = downsample_uniform(x_hr, y_norm, factor=factor)

    # 3) Hurst and D_target
    H = hurst_rs(y_norm)  # can use LR or HR; HR gives more samples
    D_target = 2.0 - H

    # 4) AMPSO optimize d_n
    dn_opt = ampspso_optimize_dn(
        xs=xs_lr, ys_anchor=ys_lr, x_hr=x_hr, y_hr=y_norm,
        D_target=D_target, mu=mu, cfg=pso_cfg, K=K, lam_smooth=lam_smooth, seed=seed
    )

    # 5) FIF seed at HR
    fif_seed = fif_eval_grid(x_hr, xs_lr, ys_lr, dn_opt, K=K)

    # 6) BiLSTM training on FIF neighborhoods
    bilstm, info = train_bilstm_on_fif(fif_seed, y_norm, cfg=train_cfg)

    # 7) Evaluate on training well
    yhat_norm = bilstm_infer(bilstm, fif_seed, win=train_cfg.win)
    metrics = compute_metrics(y_norm, yhat_norm)

    model = SRModel(
        xs_lr=xs_lr, ys_anchor=ys_lr, dn=dn_opt, bilstm=bilstm,
        norm_stats=norm_stats, win=train_cfg.win, K=K
    )
    return model, {"H": H, "D_target": D_target, **metrics, **info}

def infer_sr_on_well(
    model: SRModel,
    depth_lr: np.ndarray, curve_lr: np.ndarray,
    depth_hr_target: np.ndarray,
    mu: float = 1e-2,
    lam_smooth: float = 1e-3,
    reoptimize_dn: bool = True,
    seed: int = 2025
) -> Tuple[np.ndarray, Dict[str,float]]:
    """
    Inference on a new well with only LR data. If reoptimize_dn=True, run AMPSO with only
    D penalty + smoothness (no HR MSE). Otherwise reuse model.dn (projected to the anchors).
    Returns SR estimate on depth_hr_target (normalized to [0,1]) and aux info (H, D_target).
    """
    # Normalize LR to training scale so FIF/BiLSTM is in the same [0,1] space
    y_lr_norm = (curve_lr - model.norm_stats.xmin) / (model.norm_stats.xmax - model.norm_stats.xmin + 1e-12)
    xs_lr = depth_lr
    # Compute H, D_target on this LR curve
    H = hurst_rs(y_lr_norm)
    D_target = 2.0 - H

    # Decide anchors (use LR samples)
    ys_anchor = y_lr_norm.copy()

    # Optionally re-optimize d_n using penalties only (no HR)
    if reoptimize_dn:
        dn = ampspso_optimize_dn(
            xs=xs_lr, ys_anchor=ys_anchor, x_hr=depth_hr_target, y_hr=None,
            D_target=D_target, mu=mu, cfg=AMPSOConfig(iters=150, p_mut=0.7, swarm_size=40),
            K=model.K, lam_smooth=lam_smooth, seed=seed
        )
    else:
        # Reuse model.dn, but lengths must match number of intervals
        Nint = len(xs_lr) - 1
        dn = np.interp(np.linspace(0, 1, Nint), np.linspace(0,1,len(model.dn)), model.dn)
        dn = np.clip(dn, -0.95, 0.95)

    # FIF seed at the desired HR grid
    fif_seed = fif_eval_grid(depth_hr_target, xs_lr, ys_anchor, dn, K=model.K)

    # BiLSTM inference
    yhat_norm = bilstm_infer(model.bilstm, fif_seed, win=model.win)

    return yhat_norm, {"H": H, "D_target": D_target}

# -----------------------------
# 7) Usage examples
# -----------------------------

# Training on a single well (HR available):
# model, train_info = train_sr_model_on_well(depth_hr, gr_hr, factor=4, mu=1e-2, lam_smooth=0.0, K=8,
#     pso_cfg=AMPSOConfig(swarm_size=40, iters=200, p_mut=0.7),
#     train_cfg=TrainConfig(win=21, epochs=40, batch_size=256, lr=1e-3))
# print(train_info)

# Inference on new LR well:
# depth_hr_target = np.arange(depth_lr[0], depth_lr[-1] + 1e-9, step=(depth_lr[1]-depth_lr[0])/4.0)
# yhat_norm, info = infer_sr_on_well(model, depth_lr, gr_lr, depth_hr_target,
#     mu=1e-2, lam_smooth=1e-3, reoptimize_dn=True)
# yhat = denormalize_01(yhat_norm, model.norm_stats)
# print(info)

# Baseline: cubic interpolation for quick comparison
# def cubic_interp(depth_lr, y_lr, depth_hr_target):
#     f = interp1d(depth_lr, y_lr, kind='cubic', fill_value='extrapolate')
#     return f(depth_hr_target)
```

```I created two markdown files in this chat:

- guideline.md: A concise theoretical background and replication guide. It covers fractal characteristics of well logs, the FIF construction and dimension relation, the AMPSO objective and hyperparameters, the BiLSTM refinement rationale, preprocessing/normalization, evaluation metrics, and an end-to-end recipe for training and inference.

- code_impelement.md: A self-contained Python implementation that matches the guideline. It includes utilities (normalization, downsampling, windowing), Hurst exponent (R/S) estimation, FIF construction and evaluation, fractal-dimension solver, AMPSO optimizer for vertical scaling factors, a BiLSTM model with training/inference helpers, metrics and FFT utilities, and end-to-end train/infer functions with usage examples. Dependencies are numpy, scipy, scikit-learn, and PyTorch.

You can open or download both files from the Files/Code playground panel. If you’d like, I can also package this into a single Jupyter notebook, or generate a minimal requirements.txt for your environment.
