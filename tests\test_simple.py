import sys
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))

def test_basic_functionality():
    """Test basic functionality of all modules."""
    print("Testing basic functionality...")
    
    # Test utils
    from log_fractal_interpolation import normalize_01, compute_metrics
    data = [1.0, 2.0, 3.0, 4.0]
    normed, stats = normalize_01(data)
    assert normed == [0.0, 1/3, 2/3, 1.0]
    print("✓ Utils normalization works")
    
    # Test metrics
    metrics = compute_metrics([1,2,3], [1.1,2.1,2.9])
    assert 'RMSE' in metrics
    print("✓ Metrics computation works")
    
    # Test FIF
    from log_fractal_interpolation import hurst_rs, compute_ifs_params
    H = hurst_rs([1,2,3,4,5,6,7,8,9,10])
    assert 0.1 <= H <= 0.9
    print(f"✓ Hurst exponent works: H = {H:.3f}")
    
    # Test IFS params
    params = compute_ifs_params([0,1,2], [0,1,0], [0.1,0.2])
    assert len(params.a) == 2
    print("✓ IFS parameter computation works")
    
    # Test AMPSO
    from log_fractal_interpolation import AMPSOConfig, optimize
    def simple_fn(x):
        return sum(xi**2 for xi in x)
    
    config = AMPSOConfig(swarm_size=10, iters=20)
    best_pos, best_fit = optimize(simple_fn, dim=2, cfg=config, seed=42)
    assert len(best_pos) == 2
    assert best_fit < 1.0  # Should find minimum near origin
    print(f"✓ AMPSO optimization works: fitness = {best_fit:.4f}")
    
    # Test BiLSTM
    from log_fractal_interpolation import DummyBiLSTM, BiLSTMConfig
    config = BiLSTMConfig(window_size=3, epochs=5, hidden_size=4)
    model = DummyBiLSTM(config)
    
    # Simple prediction test
    window = [[[0.1]], [[0.2]], [[0.3]]]
    pred = model.predict_single(window)
    assert isinstance(pred, float)
    print("✓ BiLSTM prediction works")
    
    print("🎉 All basic functionality tests passed!")

def test_integration():
    """Test integration of components."""
    print("\nTesting component integration...")
    
    from log_fractal_interpolation import (
        normalize_01, downsample_uniform, hurst_rs,
        compute_ifs_params, fif_eval_grid, 
        AMPSOConfig, optimize, fitness_dn,
        train_bilstm_on_fif, BiLSTMConfig
    )
    
    # Generate simple test data
    import math
    depth = [i * 0.1 for i in range(50)]
    values = [math.sin(i * 0.2) + 1.0 for i in range(50)]
    
    # Normalize
    values_norm, stats = normalize_01(values)
    print(f"✓ Data normalized: range [{min(values_norm):.3f}, {max(values_norm):.3f}]")
    
    # Downsample
    depth_lr, values_lr = downsample_uniform(depth, values_norm, factor=4)
    print(f"✓ Downsampled: {len(depth)} -> {len(depth_lr)} points")
    
    # Hurst exponent
    H = hurst_rs(values_norm)
    D_target = 2.0 - H
    print(f"✓ Hurst exponent: {H:.3f}, Target D: {D_target:.3f}")
    
    # Simple FIF evaluation
    d_simple = [0.1] * (len(depth_lr) - 1)
    fif_result = fif_eval_grid(depth[:20], depth_lr, values_lr, d_simple, K=5)
    print(f"✓ FIF evaluation: generated {len(fif_result)} points")
    
    # AMPSO optimization (simplified)
    def simple_objective(d):
        try:
            return fitness_dn(d, depth_lr, values_lr, depth[:20], values_norm[:20], 
                            D_target, mu=0.1, K=4, lam_smooth=0.01)
        except:
            return 1000.0
    
    config = AMPSOConfig(swarm_size=15, iters=30)
    best_d, fitness = optimize(simple_objective, dim=len(depth_lr)-1, 
                              bounds=(-0.5, 0.5), cfg=config, seed=42)
    print(f"✓ AMPSO optimization: fitness = {fitness:.4f}")
    
    # FIF with optimized parameters
    fif_optimized = fif_eval_grid(depth[:20], depth_lr, values_lr, best_d, K=5)
    
    # BiLSTM training
    bilstm_config = BiLSTMConfig(window_size=5, epochs=10, hidden_size=8)
    model, training_metrics = train_bilstm_on_fif(fif_optimized, values_norm[:20], bilstm_config)
    print(f"✓ BiLSTM training: improvement = {training_metrics.get('improvement', 0):.4f}")
    
    print("🎉 Integration test passed!")

if __name__ == "__main__":
    print("="*60)
    print("SIMPLIFIED COMPREHENSIVE TESTS")
    print("="*60)
    
    try:
        test_basic_functionality()
        test_integration()
        print("\n" + "="*60)
        print("✅ ALL TESTS PASSED SUCCESSFULLY!")
        print("🔬 The fractal interpolation system is working correctly.")
        print("📚 Implementation follows the Guide.md specifications.")
        print("="*60)
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
