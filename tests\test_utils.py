import sys
import math
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))

from log_fractal_interpolation import (
    normalize_01,
    denormalize_01,
    downsample_uniform,
    make_windows_1d,
    compute_metrics,
    fft_magnitude,
    linear_interpolation,
    NormStats,
)


def test_normalize_and_denormalize_roundtrip():
    """Test normalization and denormalization roundtrip."""
    data = [1.0, 2.0, 3.0]
    normed, stats = normalize_01(data)
    restored = denormalize_01(normed, stats)
    assert all(abs(a - b) < 1e-9 for a, b in zip(data, restored))


def test_normalize_edge_cases():
    """Test normalization edge cases."""
    # Single value
    data = [5.0]
    normed, stats = normalize_01(data)
    assert normed == [0.0]  # Single value normalizes to 0
    assert stats.xmin == 5.0
    assert stats.xmax == 5.0
    
    # Constant values
    data = [2.0, 2.0, 2.0]
    normed, stats = normalize_01(data)
    assert all(x == 0.0 for x in normed)  # All normalize to 0


def test_normstats_denormalise():
    """Test NormStats.denormalise method."""
    stats = NormStats(xmin=1.0, xmax=5.0)
    normed = [0.0, 0.5, 1.0]
    restored = stats.denormalise(normed)
    expected = [1.0, 3.0, 5.0]
    assert all(abs(a - b) < 1e-9 for a, b in zip(restored, expected))


def test_downsample_uniform_factor():
    """Test uniform downsampling."""
    x = list(range(10))
    y = [v * 2 for v in range(10)]
    xs, ys = downsample_uniform(x, y, factor=2)
    assert len(xs) == 5
    assert xs[1] == 2
    assert ys[2] == 8


def test_downsample_uniform_edge_cases():
    """Test downsampling edge cases."""
    # Factor = 2
    x = [0, 1, 2, 3, 4]
    y = [0, 1, 4, 9, 16]
    xs, ys = downsample_uniform(x, y, factor=2)
    assert xs == [0, 2, 4]
    assert ys == [0, 4, 16]
    
    # Invalid factor
    try:
        downsample_uniform(x, y, factor=1)
        assert False, "Should raise ValueError"
    except ValueError:
        pass


def test_make_windows_1d_shape():
    """Test windowing function shape."""
    seq = [float(v) for v in range(5)]
    windows = make_windows_1d(seq, win=3)
    assert len(windows) == 5
    assert len(windows[0]) == 3
    # first window should repeat edge value
    assert windows[0][0][0] == seq[0]
    assert windows[-1][-1][0] == seq[-1]


def test_make_windows_1d_content():
    """Test windowing function content."""
    seq = [1.0, 2.0, 3.0, 4.0, 5.0]
    windows = make_windows_1d(seq, win=3)
    
    # Check specific windows
    assert windows[0] == [[1.0], [1.0], [2.0]]  # First window (padded)
    assert windows[2] == [[2.0], [3.0], [4.0]]  # Middle window
    assert windows[4] == [[4.0], [5.0], [5.0]]  # Last window (padded)


def test_make_windows_1d_invalid():
    """Test windowing with invalid parameters."""
    seq = [1.0, 2.0, 3.0]
    
    # Even window size
    try:
        make_windows_1d(seq, win=4)
        assert False, "Should raise ValueError"
    except ValueError:
        pass


def test_compute_metrics_perfect():
    """Test metrics computation with perfect predictions."""
    y_true = [1.0, 2.0, 3.0, 4.0, 5.0]
    y_pred = [1.0, 2.0, 3.0, 4.0, 5.0]
    
    metrics = compute_metrics(y_true, y_pred)
    
    assert metrics["RMSE"] < 1e-10
    assert metrics["MAE"] < 1e-10
    assert abs(metrics["R2"] - 1.0) < 1e-6
    assert abs(metrics["PCC"] - 1.0) < 1e-6
    assert metrics["PSNR"] > 100  # Very high for perfect match


def test_compute_metrics_typical():
    """Test metrics computation with typical predictions."""
    y_true = [1.0, 2.0, 3.0, 4.0, 5.0]
    y_pred = [1.1, 1.9, 3.1, 3.9, 5.1]
    
    metrics = compute_metrics(y_true, y_pred)
    
    assert 0.0 < metrics["RMSE"] < 1.0
    assert 0.0 < metrics["MAE"] < 1.0
    assert 0.8 < metrics["R2"] < 1.0
    assert 0.8 < metrics["PCC"] < 1.0
    assert metrics["PSNR"] > 10


def test_compute_metrics_edge_cases():
    """Test metrics with edge cases."""
    # Empty sequences
    metrics = compute_metrics([], [])
    assert all(v == 0.0 for v in metrics.values())
    
    # Single value
    metrics = compute_metrics([1.0], [1.1])
    assert abs(metrics["RMSE"] - 0.1) < 1e-10
    assert abs(metrics["MAE"] - 0.1) < 1e-10
    
    # Mismatched lengths
    try:
        compute_metrics([1, 2, 3], [1, 2])
        assert False, "Should raise ValueError"
    except ValueError:
        pass


def test_fft_magnitude_simple():
    """Test FFT magnitude computation."""
    # Simple sinusoidal signal
    n = 64
    y = [math.sin(2 * math.pi * i / n) for i in range(n)]
    
    magnitudes = fft_magnitude(y)
    
    # Should have n//2 + 1 frequency bins
    assert len(magnitudes) == n // 2 + 1
    
    # All magnitudes should be non-negative
    assert all(m >= 0 for m in magnitudes)
    
    # DC component should be small (sine wave has zero mean)
    assert magnitudes[0] < 0.1


def test_fft_magnitude_constant():
    """Test FFT of constant signal."""
    y = [1.0] * 32
    magnitudes = fft_magnitude(y)
    
    # Only DC component should be non-zero (after mean removal, should be all zero)
    assert magnitudes[0] < 1e-10  # DC removed
    assert all(m < 1e-10 for m in magnitudes[1:])  # No other frequencies


def test_linear_interpolation_simple():
    """Test linear interpolation."""
    x_lr = [0.0, 1.0, 2.0]
    y_lr = [0.0, 1.0, 4.0]
    x_hr = [0.0, 0.5, 1.0, 1.5, 2.0]
    
    result = linear_interpolation(x_lr, y_lr, x_hr)
    
    expected = [0.0, 0.5, 1.0, 2.5, 4.0]
    assert all(abs(r - e) < 1e-10 for r, e in zip(result, expected))


def test_linear_interpolation_edge_cases():
    """Test linear interpolation edge cases."""
    x_lr = [1.0, 2.0, 3.0]
    y_lr = [10.0, 20.0, 30.0]
    
    # Extrapolation (outside bounds)
    x_hr = [0.5, 3.5]
    result = linear_interpolation(x_lr, y_lr, x_hr)
    
    assert result[0] == 10.0  # Before first point
    assert result[1] == 30.0  # After last point
    
    # Exact points
    x_hr = [1.0, 2.0, 3.0]
    result = linear_interpolation(x_lr, y_lr, x_hr)
    
    assert result == [10.0, 20.0, 30.0]


def test_linear_interpolation_invalid():
    """Test linear interpolation with invalid inputs."""
    # Mismatched lengths
    try:
        linear_interpolation([1, 2], [1, 2, 3], [1.5])
        assert False, "Should raise ValueError"
    except ValueError:
        pass


def test_comprehensive_workflow():
    """Test a complete workflow using multiple utilities."""
    # Create synthetic well log data
    depth = [i * 0.1 for i in range(100)]  # 0.0 to 9.9
    values = [math.sin(i * 0.1) + 0.5 * math.cos(i * 0.3) + 1.0 for i in range(100)]
    
    # Normalize
    values_norm, stats = normalize_01(values)
    assert all(0.0 <= v <= 1.0 for v in values_norm)
    
    # Downsample
    depth_lr, values_lr = downsample_uniform(depth, values_norm, factor=4)
    assert len(depth_lr) == 25
    assert len(values_lr) == 25
    
    # Create HR grid
    depth_hr = [i * 0.025 for i in range(400)]  # Higher resolution
    
    # Linear interpolation baseline
    values_interp = linear_interpolation(depth_lr, values_lr, depth_hr)
    assert len(values_interp) == len(depth_hr)
    
    # Create windows for BiLSTM
    windows = make_windows_1d(values_interp[:100], win=5)  # First 100 points
    assert len(windows) == 100
    
    # Compute metrics (using original as reference)
    metrics = compute_metrics(values_norm[:100], values_interp[:100])
    assert isinstance(metrics, dict)
    assert all(isinstance(v, float) for v in metrics.values())
    
    # FFT analysis
    fft_orig = fft_magnitude(values_norm)
    fft_interp = fft_magnitude(values_interp[:100])
    assert len(fft_orig) == len(fft_interp)
    
    # Denormalize final result
    values_final = denormalize_01(values_interp[:100], stats)
    assert len(values_final) == 100


if __name__ == "__main__":
    test_normalize_and_denormalize_roundtrip()
    test_normalize_edge_cases()
    test_normstats_denormalise()
    test_downsample_uniform_factor()
    test_downsample_uniform_edge_cases()
    test_make_windows_1d_shape()
    test_make_windows_1d_content()
    test_make_windows_1d_invalid()
    test_compute_metrics_perfect()
    test_compute_metrics_typical()
    test_compute_metrics_edge_cases()
    test_fft_magnitude_simple()
    test_fft_magnitude_constant()
    test_linear_interpolation_simple()
    test_linear_interpolation_edge_cases()
    test_linear_interpolation_invalid()
    test_comprehensive_workflow()
    print("All utils tests passed!")
