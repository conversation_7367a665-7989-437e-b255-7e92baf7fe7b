"""Adaptive Mutation Particle Swarm Optimisation (AMPSO) utilities.

Implementation of AMPSO for optimizing vertical scaling factors of 
fractal interpolation functions, following the formulation in Guide.md.
"""

from __future__ import annotations
import math
import random
from dataclasses import dataclass
from typing import Callable, List, Optional, Tuple


@dataclass
class AMPSOConfig:
    """Configuration for AMPSO optimizer."""
    swarm_size: int = 40
    iters: int = 200
    c1: float = 2.0  # cognitive parameter
    c2: float = 2.0  # social parameter
    w_start: float = 0.9  # initial inertia weight
    w_end: float = 0.4   # final inertia weight
    p_mut: float = 0.7   # mutation probability
    gamma: float = 1.0   # velocity scaling
    d_clip: float = 0.95  # ensure |d| < 1 for contractivity


def inertia_nonlinear(t: int, T: int, w_start: float = 0.9, w_end: float = 0.4) -> float:
    """
    Nonlinear inertia schedule (Eq. 26 from Guide.md).
    
    Args:
        t: current iteration
        T: total iterations
        w_start: initial inertia weight
        w_end: final inertia weight
    
    Returns:
        Inertia weight for current iteration
    """
    if T <= 0:
        return w_end
    ratio = t / T
    return w_start - (w_start - w_end) * (1.0 - math.exp(-15.0 * (ratio ** 4)))


class Particle:
    """Individual particle in the swarm."""
    
    def __init__(self, dim: int, bounds: Tuple[float, float], rng: random.Random):
        self.dim = dim
        self.bounds = bounds
        low, high = bounds
        
        # Initialize position and velocity
        self.position = [rng.uniform(low, high) for _ in range(dim)]
        self.velocity = [0.0 for _ in range(dim)]
        
        # Personal best
        self.best_position = self.position.copy()
        self.best_fitness = float('inf')
        self.fitness = float('inf')
    
    def update_velocity(self, global_best: List[float], w: float, c1: float, c2: float, 
                       rng: random.Random):
        """Update particle velocity using PSO equation."""
        for i in range(self.dim):
            r1 = rng.random()
            r2 = rng.random()
            
            cognitive = c1 * r1 * (self.best_position[i] - self.position[i])
            social = c2 * r2 * (global_best[i] - self.position[i])
            
            self.velocity[i] = w * self.velocity[i] + cognitive + social
    
    def update_position(self, gamma: float):
        """Update particle position."""
        for i in range(self.dim):
            self.position[i] += gamma * self.velocity[i]
            
        # Clip to bounds
        low, high = self.bounds
        for i in range(self.dim):
            self.position[i] = max(low, min(high, self.position[i]))
    
    def mutate(self, p_mut: float, rng: random.Random):
        """Apply mutation with given probability."""
        for i in range(self.dim):
            if rng.random() < p_mut:
                # Mutation range [-0.2, 0.2] as in Eq. 27
                self.position[i] = (2 * rng.random() - 1) / 5.0
                
        # Clip after mutation
        low, high = self.bounds
        for i in range(self.dim):
            self.position[i] = max(low, min(high, self.position[i]))
    
    def evaluate(self, fitness_fn: Callable[[List[float]], float]):
        """Evaluate fitness and update personal best."""
        self.fitness = fitness_fn(self.position)
        
        if self.fitness < self.best_fitness:
            self.best_fitness = self.fitness
            self.best_position = self.position.copy()


def optimize(fitness_fn: Callable[[List[float]], float], 
             dim: int,
             bounds: Optional[Tuple[float, float]] = None,
             cfg: Optional[AMPSOConfig] = None,
             seed: Optional[int] = None) -> Tuple[List[float], float]:
    """
    AMPSO optimization with adaptive mutation.
    
    Args:
        fitness_fn: objective function to minimize
        dim: dimension of search space
        bounds: (low, high) bounds for each dimension
        cfg: AMPSO configuration
        seed: random seed for reproducibility
    
    Returns:
        Tuple of (best_position, best_fitness)
    """
    if cfg is None:
        cfg = AMPSOConfig()
    
    if bounds is None:
        bounds = (-cfg.d_clip, cfg.d_clip)
    
    # Initialize random number generator
    rng = random.Random(seed)
    
    # Initialize swarm
    swarm = [Particle(dim, bounds, rng) for _ in range(cfg.swarm_size)]
    
    # Evaluate initial population
    for particle in swarm:
        particle.evaluate(fitness_fn)
    
    # Find global best
    global_best_particle = min(swarm, key=lambda p: p.best_fitness)
    global_best_position = global_best_particle.best_position.copy()
    global_best_fitness = global_best_particle.best_fitness
    
    # Main optimization loop
    for t in range(1, cfg.iters + 1):
        # Update inertia weight
        w = inertia_nonlinear(t, cfg.iters, cfg.w_start, cfg.w_end)
        
        # Update each particle
        for particle in swarm:
            # Update velocity and position
            particle.update_velocity(global_best_position, w, cfg.c1, cfg.c2, rng)
            particle.update_position(cfg.gamma)
            
            # Apply mutation
            particle.mutate(cfg.p_mut, rng)
            
            # Evaluate new position
            particle.evaluate(fitness_fn)
            
            # Update global best
            if particle.best_fitness < global_best_fitness:
                global_best_fitness = particle.best_fitness
                global_best_position = particle.best_position.copy()
    
    return global_best_position, global_best_fitness


def fitness_dn(d: List[float], 
               xs: List[float], 
               ys_anchor: List[float], 
               x_hr: List[float], 
               y_hr: Optional[List[float]],
               D_target: float, 
               mu: float, 
               K: int = 8, 
               lam_smooth: float = 0.0) -> float:
    """
    Objective function for optimizing vertical scaling factors.
    
    Args:
        d: vertical scaling factors
        xs: x-coordinates of anchor points
        ys_anchor: y-coordinates of anchor points
        x_hr: high-resolution x grid
        y_hr: high-resolution y targets (None for inference)
        D_target: target fractal dimension
        mu: weight for fractal dimension penalty
        K: recursion depth for FIF evaluation
        lam_smooth: weight for smoothness penalty
    
    Returns:
        Objective value (lower is better)
    """
    try:
        from .fif import compute_ifs_params, fractal_dimension_from_ad, fif_eval_grid
        
        # Compute fractal dimension penalty
        params = compute_ifs_params(xs, ys_anchor, d)
        Df = fractal_dimension_from_ad(params.a, d)
        
        if math.isnan(Df):
            dim_penalty = 1000.0  # Large penalty for invalid dimension
        else:
            dim_penalty = (Df - D_target) ** 2
        
        # Smoothness penalty
        smooth_penalty = 0.0
        if lam_smooth > 0 and len(d) > 1:
            smooth_penalty = sum((d[i+1] - d[i])**2 for i in range(len(d)-1)) / len(d)
        
        # MSE penalty (only if HR data available)
        mse_penalty = 0.0
        if y_hr is not None:
            try:
                y_seed = fif_eval_grid(x_hr, xs, ys_anchor, d, K=K)
                mse_penalty = sum((y_hr[i] - y_seed[i])**2 for i in range(len(y_hr))) / len(y_hr)
            except Exception:
                mse_penalty = 1000.0  # Large penalty for evaluation errors
        
        return mse_penalty + mu * dim_penalty + lam_smooth * smooth_penalty
        
    except Exception:
        return 1000.0  # Large penalty for any errors

