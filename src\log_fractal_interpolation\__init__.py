"""Top level package for log fractal interpolation utilities."""

from .utils import (
    NormStats,
    normalize_01,
    denormalize_01,
    downsample_uniform,
    make_windows_1d,
    compute_metrics,
    fft_magnitude,
    linear_interpolation,
)
from .ampso import (
    AMPSOConfig,
    optimize,
    fitness_dn,
    inertia_nonlinear,
)
from .bilstm import (
    DummyBiLSTM,
    BiLSTMConfig,
    train_bilstm_on_fif,
    bilstm_infer,
)
from .fif import (
    placeholder_fif,
    IFSParams,
    compute_ifs_params,
    fif_eval_grid,
    fractal_dimension_from_ad,
    hurst_rs,
)

__all__ = [
    # Utils
    "NormStats",
    "normalize_01",
    "denormalize_01",
    "downsample_uniform",
    "make_windows_1d",
    "compute_metrics",
    "fft_magnitude",
    "linear_interpolation",
    # AMPSO
    "AMPSOConfig",
    "optimize",
    "fitness_dn",
    "inertia_nonlinear",
    # BiLSTM
    "DummyBiLSTM",
    "BiLSTMConfig",
    "train_bilstm_on_fif",
    "bilstm_infer",
    # FIF
    "placeholder_fif",
    "IFSParams",
    "compute_ifs_params",
    "fif_eval_grid",
    "fractal_dimension_from_ad",
    "hurst_rs",
]
