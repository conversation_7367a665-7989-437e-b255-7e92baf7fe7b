import sys
import math
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))

from log_fractal_interpolation import (
    IFSParams,
    compute_ifs_params,
    fif_eval_grid,
    fractal_dimension_from_ad,
    hurst_rs,
    placeholder_fif,
)


def test_placeholder_fif():
    """Test that placeholder FIF returns input unchanged."""
    # Test with a simple sequence
    input_seq = [1.0, 2.0, 3.0, 4.0]
    output_seq = placeholder_fif(input_seq)
    assert output_seq == input_seq
    
    # Test with an empty sequence
    input_seq = []
    output_seq = placeholder_fif(input_seq)
    assert output_seq == input_seq
    
    # Test with a single element
    input_seq = [5.0]
    output_seq = placeholder_fif(input_seq)
    assert output_seq == input_seq


def test_compute_ifs_params():
    """Test IFS parameter computation."""
    # Simple test case: linear function
    xs = [0.0, 0.5, 1.0]
    ys = [0.0, 0.5, 1.0]
    d = [0.1, 0.2]  # Two intervals
    
    params = compute_ifs_params(xs, ys, d)
    
    # Check that we get the right structure
    assert isinstance(params, IFSParams)
    assert len(params.a) == 2
    assert len(params.c) == 2
    assert len(params.e) == 2
    assert len(params.f) == 2
    assert len(params.d) == 2
    
    # Check some basic properties
    assert all(isinstance(x, float) for x in params.a)
    assert params.d == list(d)


def test_fif_eval_grid_simple():
    """Test FIF evaluation on a simple case."""
    # Linear anchor points
    xs = [0.0, 1.0]
    ys = [0.0, 1.0]
    d = [0.1]  # One interval
    
    # Evaluate at anchor points - should return exact values
    result = fif_eval_grid(xs, xs, ys, d, K=5)
    
    # Should approximately match anchor points
    assert abs(result[0] - ys[0]) < 0.1
    assert abs(result[1] - ys[1]) < 0.1


def test_fif_eval_grid_interpolation():
    """Test FIF evaluation with interpolation."""
    # Simple case with 3 anchor points
    xs = [0.0, 0.5, 1.0]
    ys = [0.0, 1.0, 0.0]  # Triangle shape
    d = [0.2, -0.2]  # Two intervals
    
    # Evaluate at more points
    x_eval = [0.0, 0.25, 0.5, 0.75, 1.0]
    result = fif_eval_grid(x_eval, xs, ys, d, K=6)
    
    # Should have 5 results
    assert len(result) == 5
    
    # Should approximate anchor points
    assert abs(result[0] - 0.0) < 0.2  # x=0.0 -> y≈0.0
    assert abs(result[2] - 1.0) < 0.2  # x=0.5 -> y≈1.0
    assert abs(result[4] - 0.0) < 0.2  # x=1.0 -> y≈0.0


def test_fractal_dimension_computation():
    """Test fractal dimension computation."""
    # Simple case that should have solution
    # Choose values such that sum |d_i| |a_i|^{D-1} = 1 has a solution in [1, 2]
    a = [0.6, 0.4]
    d = [0.8, 0.7]
    
    D = fractal_dimension_from_ad(a, d)
    
    # Should be between 1 and 2, or NaN if no solution exists
    if not math.isnan(D):
        assert 1.0 <= D <= 2.0
    else:
        # If NaN, try different values that should work
        a = [0.3, 0.3]
        d = [1.5, 1.5]
        D = fractal_dimension_from_ad(a, d)
        if not math.isnan(D):
            assert 1.0 <= D <= 2.0


def test_fractal_dimension_edge_cases():
    """Test fractal dimension edge cases."""
    # Case with no solution (same sign at endpoints)
    a = [0.9, 0.9]
    d = [0.9, 0.9]
    
    D = fractal_dimension_from_ad(a, d)
    # Might return NaN for impossible cases
    assert math.isnan(D) or (1.0 <= D <= 2.0)


def test_hurst_exponent_simple():
    """Test Hurst exponent estimation."""
    # Simple deterministic sequence
    n = 100
    x = [i + 0.1 * math.sin(i * 0.1) for i in range(n)]
    
    H = hurst_rs(x)
    
    # Should be between 0 and 1
    assert 0.1 <= H <= 0.9


def test_hurst_exponent_trend():
    """Test Hurst exponent with trending data."""
    # Trending sequence (should have H > 0.5)
    x = [i + 0.5 * i for i in range(50)]
    
    H = hurst_rs(x)
    
    # Trending data typically has H > 0.5
    assert 0.1 <= H <= 0.9


def test_hurst_exponent_short_series():
    """Test Hurst exponent with short series."""
    # Very short series
    x = [1.0, 2.0, 1.5, 2.5]
    
    H = hurst_rs(x)
    
    # Should return fallback value for short series
    assert H == 0.5


def test_error_handling():
    """Test error handling in FIF functions."""
    # Mismatched lengths
    try:
        compute_ifs_params([0, 1], [0, 1, 2], [0.1])
        assert False, "Should raise ValueError"
    except ValueError:
        pass
    
    # Wrong d length
    try:
        compute_ifs_params([0, 1, 2], [0, 1, 2], [0.1])  # Need 2 d values for 3 points
        assert False, "Should raise ValueError"
    except ValueError:
        pass


if __name__ == "__main__":
    test_placeholder_fif()
    test_compute_ifs_params()
    test_fif_eval_grid_simple()
    test_fif_eval_grid_interpolation()
    test_fractal_dimension_computation()
    test_fractal_dimension_edge_cases()
    test_hurst_exponent_simple()
    test_hurst_exponent_trend()
    test_hurst_exponent_short_series()
    test_error_handling()
    print("All FIF tests passed!")