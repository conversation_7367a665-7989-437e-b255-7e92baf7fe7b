import sys
import math
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))

from log_fractal_interpolation import (
    AMPSOConfig,
    optimize,
    fitness_dn,
    inertia_nonlinear,
)


def test_ampso_config():
    """Test AMPSO configuration dataclass."""
    config = AMPSOConfig()
    
    # Test default values
    assert config.swarm_size == 40
    assert config.iters == 200
    assert config.c1 == 2.0
    assert config.c2 == 2.0
    assert config.w_start == 0.9
    assert config.w_end == 0.4
    assert config.p_mut == 0.7
    
    # Test custom values
    custom_config = AMPSOConfig(swarm_size=20, iters=100)
    assert custom_config.swarm_size == 20
    assert custom_config.iters == 100


def test_inertia_nonlinear():
    """Test nonlinear inertia weight schedule."""
    # Test at beginning (t=0)
    w0 = inertia_nonlinear(0, 100, 0.9, 0.4)
    assert abs(w0 - 0.9) < 0.01  # Should be close to w_start
    
    # Test at end (t=T)
    wT = inertia_nonlinear(100, 100, 0.9, 0.4)
    assert abs(wT - 0.4) < 0.05  # Should be close to w_end
    
    # Test middle values decrease monotonically
    w25 = inertia_nonlinear(25, 100, 0.9, 0.4)
    w50 = inertia_nonlinear(50, 100, 0.9, 0.4)
    w75 = inertia_nonlinear(75, 100, 0.9, 0.4)
    
    assert w0 > w25 > w50 > w75 > wT
    
    # Test edge case
    w_edge = inertia_nonlinear(10, 0)  # T=0
    assert w_edge == 0.4  # Should return w_end


def test_optimize_simple():
    """Test AMPSO optimization on simple function."""
    # Simple quadratic function: f(x) = (x[0] - 1)^2 + (x[1] - 2)^2
    def quadratic(x):
        return (x[0] - 1.0)**2 + (x[1] - 2.0)**2
    
    # Run optimization
    config = AMPSOConfig(swarm_size=20, iters=50, p_mut=0.5)
    best_pos, best_fit = optimize(quadratic, dim=2, bounds=(-5.0, 5.0), cfg=config, seed=42)
    
    # Check that we found a reasonable solution
    assert len(best_pos) == 2
    assert isinstance(best_fit, float)
    
    # Should be close to global minimum at (1, 2)
    assert abs(best_pos[0] - 1.0) < 0.5
    assert abs(best_pos[1] - 2.0) < 0.5
    assert best_fit < 1.0  # Should be small


def test_optimize_sphere():
    """Test AMPSO on n-dimensional sphere function."""
    # Sphere function: f(x) = sum(x_i^2)
    def sphere(x):
        return sum(xi**2 for xi in x)
    
    # 3D optimization
    config = AMPSOConfig(swarm_size=15, iters=30)
    best_pos, best_fit = optimize(sphere, dim=3, bounds=(-2.0, 2.0), cfg=config, seed=123)
    
    # Should find solution close to origin
    assert len(best_pos) == 3
    assert best_fit < 0.5  # Should be small
    for xi in best_pos:
        assert abs(xi) < 1.0  # Components should be small


def test_optimize_default_config():
    """Test optimization with default configuration."""
    def simple_fn(x):
        return x[0]**2
    
    # Use default config
    best_pos, best_fit = optimize(simple_fn, dim=1, seed=999)
    
    assert len(best_pos) == 1
    assert abs(best_pos[0]) < 1.0  # Should be close to 0
    assert best_fit < 1.0


def test_fitness_dn_basic():
    """Test fitness function for vertical scaling factors."""
    # Simple test case
    d = [0.1, 0.2]
    xs = [0.0, 0.5, 1.0]
    ys_anchor = [0.0, 1.0, 0.0]
    x_hr = [0.0, 0.25, 0.5, 0.75, 1.0]
    y_hr = [0.0, 0.5, 1.0, 0.5, 0.0]
    D_target = 1.5
    mu = 0.01
    
    # Should not crash and return a finite value
    fitness = fitness_dn(d, xs, ys_anchor, x_hr, y_hr, D_target, mu, K=5, lam_smooth=0.0)
    
    assert isinstance(fitness, float)
    assert not math.isnan(fitness)
    assert not math.isinf(fitness)
    assert fitness >= 0.0  # Fitness should be non-negative


def test_fitness_dn_no_hr():
    """Test fitness function without HR data (inference mode)."""
    d = [0.3, -0.1]
    xs = [0.0, 0.5, 1.0]
    ys_anchor = [1.0, 0.5, 0.8]
    x_hr = [0.0, 0.25, 0.5, 0.75, 1.0]
    y_hr = None  # No HR data
    D_target = 1.2
    mu = 0.1
    lam_smooth = 0.01
    
    # Should work without HR data (penalty-based optimization)
    fitness = fitness_dn(d, xs, ys_anchor, x_hr, y_hr, D_target, mu, K=4, lam_smooth=lam_smooth)
    
    assert isinstance(fitness, float)
    assert not math.isnan(fitness)
    assert fitness >= 0.0


def test_fitness_dn_smoothness():
    """Test smoothness penalty in fitness function."""
    d_smooth = [0.1, 0.1, 0.1]  # Smooth
    d_rough = [0.1, 0.8, -0.5]  # Rough
    
    xs = [0.0, 0.33, 0.67, 1.0]
    ys_anchor = [0.0, 0.5, 0.3, 1.0]
    x_hr = [0.0, 0.5, 1.0]
    D_target = 1.5
    mu = 0.01
    lam_smooth = 1.0  # High smoothness penalty
    
    fitness_smooth = fitness_dn(d_smooth, xs, ys_anchor, x_hr, None, D_target, mu, K=3, lam_smooth=lam_smooth)
    fitness_rough = fitness_dn(d_rough, xs, ys_anchor, x_hr, None, D_target, mu, K=3, lam_smooth=lam_smooth)
    
    # Smooth should have lower penalty than rough
    assert fitness_smooth < fitness_rough


def test_error_handling():
    """Test error handling in AMPSO functions."""
    # Test invalid fitness function
    def bad_fitness(x):
        raise RuntimeError("Simulated error")
    
    # Should handle errors gracefully
    best_pos, best_fit = optimize(bad_fitness, dim=2, 
                                 cfg=AMPSOConfig(swarm_size=5, iters=3), seed=42)
    
    # Should still return valid results (with penalty values)
    assert len(best_pos) == 2
    assert isinstance(best_fit, float)


if __name__ == "__main__":
    test_ampso_config()
    test_inertia_nonlinear()
    test_optimize_simple()
    test_optimize_sphere()
    test_optimize_default_config()
    test_fitness_dn_basic()
    test_fitness_dn_no_hr()
    test_fitness_dn_smoothness()
    test_error_handling()
    print("All AMPSO tests passed!")