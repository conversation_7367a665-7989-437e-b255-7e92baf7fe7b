"""Bi-directional LSTM model for super-resolution refinement.

This module provides a simplified BiLSTM implementation that can learn
local mappings from FIF neighborhoods to true HR samples, as described
in Guide.md.
"""

from __future__ import annotations
import math
from typing import List, Sequence, Tuple, Optional
from dataclasses import dataclass


@dataclass
class BiLSTMConfig:
    """Configuration for BiLSTM training."""
    window_size: int = 21
    hidden_size: int = 32  # Simplified for pure Python implementation
    learning_rate: float = 0.01
    epochs: int = 50
    batch_size: int = 32


class SimpleBiLSTMCell:
    """Simplified LSTM cell implementation in pure Python."""
    
    def __init__(self, input_size: int, hidden_size: int, seed: int = 42):
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # Initialize weights (simplified Xavier initialization)
        import random
        rng = random.Random(seed)
        
        def init_weights(rows: int, cols: int) -> List[List[float]]:
            scale = math.sqrt(2.0 / (rows + cols))
            return [[rng.gauss(0, scale) for _ in range(cols)] for _ in range(rows)]
        
        # LSTM weights for forget, input, candidate, output gates
        self.Wf = init_weights(hidden_size, input_size + hidden_size)
        self.Wi = init_weights(hidden_size, input_size + hidden_size)
        self.Wc = init_weights(hidden_size, input_size + hidden_size)
        self.Wo = init_weights(hidden_size, input_size + hidden_size)
        
        # Biases
        self.bf = [0.0] * hidden_size
        self.bi = [0.0] * hidden_size
        self.bc = [0.0] * hidden_size
        self.bo = [0.0] * hidden_size
    
    def sigmoid(self, x: float) -> float:
        """Sigmoid activation function."""
        return 1.0 / (1.0 + math.exp(-max(-500, min(500, x))))
    
    def tanh(self, x: float) -> float:
        """Tanh activation function."""
        return math.tanh(max(-500, min(500, x)))
    
    def forward(self, x: List[float], h_prev: List[float], c_prev: List[float]) -> Tuple[List[float], List[float]]:
        """Forward pass through LSTM cell."""
        # Concatenate input and previous hidden state
        concat = x + h_prev
        
        # Forget gate
        f = [self.sigmoid(sum(self.Wf[j][k] * concat[k] for k in range(len(concat))) + self.bf[j])
             for j in range(self.hidden_size)]
        
        # Input gate
        i_gate = [self.sigmoid(sum(self.Wi[j][k] * concat[k] for k in range(len(concat))) + self.bi[j])
                  for j in range(self.hidden_size)]
        
        # Candidate values
        c_tilde = [self.tanh(sum(self.Wc[j][k] * concat[k] for k in range(len(concat))) + self.bc[j])
                   for j in range(self.hidden_size)]
        
        # Output gate
        o = [self.sigmoid(sum(self.Wo[j][k] * concat[k] for k in range(len(concat))) + self.bo[j])
             for j in range(self.hidden_size)]
        
        # Update cell state
        c = [f[j] * c_prev[j] + i_gate[j] * c_tilde[j] for j in range(self.hidden_size)]
        
        # Update hidden state
        h = [o[j] * self.tanh(c[j]) for j in range(self.hidden_size)]
        
        return h, c


class DummyBiLSTM:
    """
    Simplified BiLSTM implementation for learning FIF-to-HR mappings.
    
    This is a simplified version that demonstrates the concept without
    requiring external deep learning libraries.
    """
    
    def __init__(self, config: Optional[BiLSTMConfig] = None, seed: int = 42):
        self.config = config or BiLSTMConfig()
        self.seed = seed
        
        # Initialize forward and backward LSTM cells
        self.forward_cell = SimpleBiLSTMCell(1, self.config.hidden_size, seed)
        self.backward_cell = SimpleBiLSTMCell(1, self.config.hidden_size, seed + 1)
        
        # Output layer weights
        import random
        rng = random.Random(seed + 2)
        output_input_size = 2 * self.config.hidden_size
        scale = math.sqrt(2.0 / output_input_size)
        self.output_weights = [rng.gauss(0, scale) for _ in range(output_input_size)]
        self.output_bias = 0.0
        
        self.is_trained = False
    
    def forward_lstm(self, sequence: List[List[float]]) -> List[List[float]]:
        """Forward LSTM pass."""
        hidden_states = []
        h = [0.0] * self.config.hidden_size
        c = [0.0] * self.config.hidden_size
        
        for x in sequence:
            # Ensure x is a list of floats
            if isinstance(x, list):
                x_input = [float(val) for val in x]
            else:
                x_input = [float(x)]
            
            h, c = self.forward_cell.forward(x_input, h, c)
            hidden_states.append(h.copy())
        
        return hidden_states
    
    def backward_lstm(self, sequence: List[List[float]]) -> List[List[float]]:
        """Backward LSTM pass."""
        hidden_states = []
        h = [0.0] * self.config.hidden_size
        c = [0.0] * self.config.hidden_size
        
        for x in reversed(sequence):
            # Ensure x is a list of floats
            if isinstance(x, list):
                x_input = [float(val) for val in x]
            else:
                x_input = [float(x)]
            
            h, c = self.backward_cell.forward(x_input, h, c)
            hidden_states.append(h.copy())
        
        # Reverse to match forward order
        return list(reversed(hidden_states))
    
    def predict_single(self, window: List[List[float]]) -> float:
        """Predict center point from window."""
        if not window:
            return 0.0
        
        # Flatten the input if it's in [[[value]]] format
        flattened_window = []
        for item in window:
            if isinstance(item, list) and len(item) == 1:
                if isinstance(item[0], list) and len(item[0]) == 1:
                    flattened_window.append([item[0][0]])  # [[[x]]] -> [[x]]
                else:
                    flattened_window.append(item)  # [[x]] -> [[x]]
            else:
                flattened_window.append([item] if not isinstance(item, list) else item)
        
        # BiLSTM forward pass
        forward_states = self.forward_lstm(flattened_window)
        backward_states = self.backward_lstm(flattened_window)
        
        # Get center hidden states
        center_idx = len(flattened_window) // 2
        if center_idx >= len(forward_states) or center_idx >= len(backward_states):
            # Fallback: return mean of window values
            total = 0.0
            count = 0
            for item in flattened_window:
                if isinstance(item, list):
                    total += sum(item)
                    count += len(item)
                else:
                    total += item
                    count += 1
            return total / max(count, 1)
        
        # Concatenate forward and backward hidden states
        concat_hidden = forward_states[center_idx] + backward_states[center_idx]
        
        # Linear output layer
        output = sum(self.output_weights[i] * concat_hidden[i] for i in range(len(concat_hidden)))
        output += self.output_bias
        
        # Clamp output to reasonable range for normalized data
        output = max(-0.2, min(1.2, output))
        
        return output
    
    def predict(self, x: Sequence[Sequence[Sequence[float]]]) -> List[float]:
        """
        Predict on a batch of windows.
        
        Args:
            x: List of windows, where each window is List[List[float]]
               with shape [window_size, 1]
        
        Returns:
            List of predictions for center points
        """
        return [self.predict_single(window) for window in x]
    
    def train(self, X: List[List[List[float]]], y: List[float]) -> dict:
        """
        Simple training procedure using gradient-free optimization.
        
        This is a simplified training that adjusts weights based on
        prediction errors. In a real implementation, this would use
        backpropagation through time.
        
        Args:
            X: Training windows [batch_size, window_size, 1]
            y: Target values [batch_size]
        
        Returns:
            Training metrics dictionary
        """
        if len(X) != len(y):
            raise ValueError("X and y must have same length")
        
        initial_loss = self._compute_loss(X, y)
        
        # Simple training loop with smarter weight updates
        import random
        rng = random.Random(self.seed)
        
        best_loss = initial_loss
        best_weights = self.output_weights.copy()
        best_bias = self.output_bias
        
        # Start with larger perturbations and gradually reduce
        for epoch in range(self.config.epochs):
            # Adaptive perturbation scale
            progress = epoch / max(self.config.epochs - 1, 1)
            perturbation_scale = 0.1 * (1.0 - progress) + 0.001 * progress
            
            # Try multiple perturbations per epoch
            for trial in range(3):
                # Perturb output weights
                new_weights = [w + rng.gauss(0, perturbation_scale) for w in self.output_weights]
                new_bias = self.output_bias + rng.gauss(0, perturbation_scale)
                
                # Temporarily update weights
                old_weights = self.output_weights.copy()
                old_bias = self.output_bias
                
                self.output_weights = new_weights
                self.output_bias = new_bias
                
                # Evaluate
                loss = self._compute_loss(X, y)
                
                # Keep if better
                if loss < best_loss:
                    best_loss = loss
                    best_weights = new_weights.copy()
                    best_bias = new_bias
                else:
                    # Restore old weights
                    self.output_weights = old_weights
                    self.output_bias = old_bias
            
            # Update to best found weights every few epochs
            if epoch % 5 == 0:
                self.output_weights = best_weights.copy()
                self.output_bias = best_bias
        
        # Set final best weights
        self.output_weights = best_weights
        self.output_bias = best_bias
        
        # If training didn't improve much, try a simple linear fit approach
        if best_loss >= initial_loss * 0.95:
            # Simple linear regression approach for output layer
            self._fit_linear_output(X, y)
            final_loss = self._compute_loss(X, y)
            if final_loss < best_loss:
                best_loss = final_loss
        
        self.is_trained = True
        
        return {
            'initial_loss': initial_loss,
            'final_loss': best_loss,
            'improvement': initial_loss - best_loss
        }
    
    def _fit_linear_output(self, X: List[List[List[float]]], y: List[float]):
        """Fit output layer using simple linear regression on LSTM features."""
        # Extract features from LSTM
        features = []
        for window in X:
            # Get LSTM features
            forward_states = self.forward_lstm(window)
            backward_states = self.backward_lstm(window)
            center_idx = len(window) // 2
            if center_idx < len(forward_states) and center_idx < len(backward_states):
                concat_hidden = forward_states[center_idx] + backward_states[center_idx]
                features.append(concat_hidden)
            else:
                # Fallback feature
                avg_feature = [0.0] * (2 * self.config.hidden_size)
                features.append(avg_feature)
        
        if not features:
            return
        
        # Simple linear regression: minimize sum((w·x + b - y)²)
        n_features = len(features[0])
        n_samples = len(features)
        
        if n_samples < 2:
            return
        
        # Compute means
        y_mean = sum(y) / n_samples
        x_means = [sum(features[i][j] for i in range(n_samples)) / n_samples 
                  for j in range(n_features)]
        
        # Compute covariance and variance
        numerator = [0.0] * n_features
        denominator = 0.0
        
        for i in range(n_samples):
            y_diff = y[i] - y_mean
            denominator += y_diff * y_diff
            for j in range(n_features):
                x_diff = features[i][j] - x_means[j]
                numerator[j] += x_diff * y_diff
        
        # Update weights (simple approach)
        if denominator > 1e-10:
            scale = 0.1 / max(denominator, 1e-6)
            self.output_weights = [scale * num for num in numerator]
            self.output_bias = y_mean - sum(self.output_weights[j] * x_means[j] 
                                          for j in range(n_features))
    
    def _compute_loss(self, X: List[List[List[float]]], y: List[float]) -> float:
        """Compute mean squared error loss."""
        predictions = self.predict(X)
        mse = sum((pred - target)**2 for pred, target in zip(predictions, y)) / len(y)
        return mse


def train_bilstm_on_fif(fif_seq: List[float], 
                       y_hr: List[float], 
                       config: Optional[BiLSTMConfig] = None) -> Tuple[DummyBiLSTM, dict]:
    """
    Train BiLSTM on FIF sequence to predict HR values.
    
    Args:
        fif_seq: FIF interpolation sequence (training input)
        y_hr: High-resolution target sequence
        config: Training configuration
    
    Returns:
        Tuple of (trained_model, training_metrics)
    """
    if config is None:
        config = BiLSTMConfig()
    
    if len(fif_seq) != len(y_hr):
        raise ValueError("fif_seq and y_hr must have same length")
    
    # Create windowed data
    from .utils import make_windows_1d
    X = make_windows_1d(fif_seq, config.window_size)
    y = y_hr.copy()
    
    # Initialize and train model
    model = DummyBiLSTM(config)
    training_metrics = model.train(X, y)
    
    return model, training_metrics


def bilstm_infer(model: DummyBiLSTM, 
                fif_seq: List[float], 
                window_size: Optional[int] = None) -> List[float]:
    """
    Run inference with trained BiLSTM model.
    
    Args:
        model: Trained BiLSTM model
        fif_seq: FIF sequence to refine
        window_size: Window size (uses model config if None)
    
    Returns:
        Refined sequence predictions
    """
    if window_size is None:
        window_size = model.config.window_size
    
    from .utils import make_windows_1d
    X = make_windows_1d(fif_seq, window_size)
    return model.predict(X)

