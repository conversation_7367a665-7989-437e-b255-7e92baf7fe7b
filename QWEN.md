# Qwen Code Context for `Fractal_Interp\GPT_High`

## Project Overview

This directory contains the `log_fractal_interpolation` Python project. The primary goal is to implement a workflow for performing super-resolution on well-log data. This is achieved by combining three key techniques:

1.  **Fractal Interpolation Functions (FIF):** To upsample low-resolution anchor points while respecting the self-affine nature of well logs.
2.  **Adaptive Mutation Particle Swarm Optimisation (AMPSO):** To tune the vertical scaling factors of the FIF for optimal results.
3.  **Bi-directional LSTM (BiLSTM) refinement:** To learn residual corrections from high-resolution examples, refining the FIF output.

The implementation is intentionally lightweight and serves as a structured base for future expansion. Core algorithms are represented by small, documented placeholders (`fif.py`, `ampso.py`, `bilstm.py`), while reusable utilities are implemented in `utils.py`. A `Guide.md` file provides the theoretical background and a detailed replication guideline, including a full Python implementation.

## Codebase Structure

*   **`README.md`**: General project description, package layout, and basic development instructions (e.g., running tests with `pytest`).
*   **`Guide.md`**: Extensive documentation covering the theoretical background (fractals, FIF, AMPSO, BiLSTM) and a practical, self-contained Python implementation.
*   **`src/log_fractal_interpolation/`**: Main Python package source code.
    *   `__init__.py`: Defines the public API, currently exposing utilities from `utils.py`.
    *   `utils.py`: Contains essential utility functions for normalization (`normalize_01`, `denormalize_01`), downsampling (`downsample_uniform`), and creating sliding windows (`make_windows_1d`). This module avoids third-party dependencies.
    *   `fif.py`: **Placeholder** for Fractal Interpolation Function logic.
    *   `ampso.py`: **Placeholder** for the Adaptive Mutation Particle Swarm Optimisation routine.
    *   `bilstm.py`: **Placeholder** for the Bi-directional LSTM model.
*   **`tests/`**: Contains unit tests for the implemented utilities in `utils.py`.
    *   `test_utils.py`: Tests for normalization, downsampling, and windowing functions.

## Development Environment and Commands

*   **Language**: Python.
*   **Dependencies**: The core `utils.py` module avoids third-party libraries for minimalism. The full implementation (as per `Guide.md`) and the placeholders (`bilstm.py`) imply dependencies on `numpy`, `scipy`, `scikit-learn`, and `torch` (PyTorch).
*   **Testing**: The project uses `pytest` for running tests.
    *   Command: `pytest` (Run from the project root directory).

## Key Concepts from `Guide.md`

*   **Fractal Characteristics**: Well logs are modeled as self-affine curves, characterized by the Hurst exponent (H) and Fractal dimension (D = 2 - H).
*   **FIF Workflow**: Involves defining anchor points, calculating affine map parameters based on vertical scaling factors (d_n), evaluating the FIF via recursion, and estimating the resulting fractal dimension.
*   **AMPSO Optimization**: Used to find optimal `d_n` values by minimizing an objective function that includes MSE (if HR data is available), a penalty for deviation from a target fractal dimension, and a smoothness penalty.
*   **BiLSTM Refinement**: A neural network trained to correct the FIF output by learning mappings from local FIF neighborhoods to true high-resolution values.
*   **Data Preprocessing**: Includes handling NaNs, normalizing data to [0,1] using training statistics, and downsampling high-resolution data to create low-resolution anchors for training.
*   **End-to-End Process**:
    *   *Training*: Normalize HR data, compute H/D_target, optimize `d_n` using AMPSO (with HR MSE), render FIF seed, train BiLSTM on FIF seed vs HR.
    *   *Inference*: Normalize LR data using training stats, compute H/D_target from LR, optimize `d_n` using AMPSO (without HR MSE), render FIF seed on desired HR grid, apply trained BiLSTM.
