# Log Fractal Interpolation

This repository provides a comprehensive implementation of the workflow 
described in `Guide.md` for performing super‑resolution on well‑log data 
using a combination of:

* **Fractal Interpolation Functions (FIF)** to upsample low resolution
  anchor points while respecting the self‑affine nature of well logs.
* **Adaptive Mutation Particle Swarm Optimisation (AMPSO)** to tune the
  vertical scaling factors of the FIF.
* **Bi‑directional LSTM refinement** to learn residual corrections from
  high‑resolution examples.

The implementation is fully functional and includes comprehensive testing
to validate the complete theoretical framework from the research paper.

## Package layout

```
src/log_fractal_interpolation/
├── __init__.py        # Public package API with all components
├── ampso.py           # Complete AMPSO optimizer implementation
├── bilstm.py          # Bi-directional LSTM model (pure Python)
├── fif.py             # Fractal interpolation functions with IFS
└── utils.py           # Data processing, metrics, and utilities
```

## Key Features

### Implemented Components
- ✅ **Hurst Exponent Estimation**: R/S analysis for fractal characterization
- ✅ **FIF Construction**: Complete IFS parameter computation and evaluation
- ✅ **AMPSO Optimization**: Multi-objective optimization with adaptive mutation
- ✅ **BiLSTM Refinement**: Bi-directional LSTM for local improvements
- ✅ **Evaluation Metrics**: RMSE, MAE, R², PCC, PSNR, and spectral analysis
- ✅ **End-to-End Pipeline**: Training and inference modes

### Mathematical Validation
- Fractal dimension computation following Moran-type equations
- Contractivity conditions for IFS stability
- Nonlinear inertia scheduling for PSO convergence
- Window-based processing for local pattern learning

## Testing

A comprehensive test suite validates the entire workflow:

```bash
# Run complete functionality test
python tests/test_simple.py

# Run individual component tests
python tests/test_utils.py      # Data processing utilities
python tests/test_fif.py        # Fractal interpolation functions
python tests/test_ampso.py      # AMPSO optimization
python tests/test_bilstm.py     # BiLSTM model testing

# Run comprehensive end-to-end test
python tests/test_comprehensive.py
```

The testing demonstrates:
- Complete super-resolution workflow on synthetic well logs
- Training with HR ground truth and inference without HR data
- Component integration and error handling
- Performance validation against baseline methods

## Usage Example

```python
from log_fractal_interpolation import (
    normalize_01, downsample_uniform, hurst_rs,
    AMPSOConfig, optimize, fitness_dn,
    train_bilstm_on_fif, BiLSTMConfig
)

# 1. Prepare data
values_norm, stats = normalize_01(well_log_values)
depth_lr, values_lr = downsample_uniform(depth, values_norm, factor=4)

# 2. Estimate fractal characteristics
H = hurst_rs(values_norm)
D_target = 2.0 - H

# 3. Optimize FIF parameters
def objective(d):
    return fitness_dn(d, depth_lr, values_lr, depth_hr, values_norm, 
                     D_target, mu=0.01)

config = AMPSOConfig(swarm_size=40, iters=200)
best_d, _ = optimize(objective, dim=len(depth_lr)-1, cfg=config)

# 4. Generate FIF seed and refine with BiLSTM
fif_seed = fif_eval_grid(depth_hr, depth_lr, values_lr, best_d)
bilstm_config = BiLSTMConfig(window_size=21, epochs=40)
model, _ = train_bilstm_on_fif(fif_seed, values_norm, bilstm_config)
sr_result = bilstm_infer(model, fif_seed)
```

## Dependencies

The implementation uses only Python standard library modules for maximum
compatibility:
- `math` for mathematical functions  
- `random` for stochastic algorithms
- `dataclasses` for configuration structures

No external dependencies required for core functionality.

## Documentation

- `Guide.md` - Theoretical background and mathematical formulation
- `TESTING_SUMMARY.md` - Comprehensive testing documentation
- `tests/` - Complete test suite with examples

## License

This project is released under the terms of the MIT License.  See
[LICENSE](LICENSE) for details.
