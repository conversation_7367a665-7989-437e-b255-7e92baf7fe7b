"""Utility functions for log fractal interpolation.

This module implements basic preprocessing helpers referenced in
:mod:`Guide.md`.  It intentionally avoids third‑party dependencies so the
unit tests can run in a minimal environment.
"""
from __future__ import annotations

import math
from dataclasses import dataclass
from typing import Iterable, List, Sequence, Tuple, Dict


@dataclass
class NormStats:
    """Simple container for min/max normalisation statistics."""

    xmin: float
    xmax: float

    def denormalise(self, y_norm: Sequence[float]) -> List[float]:
        """Inverse of :func:`normalize_01` using the stored statistics."""
        return denormalize_01(y_norm, self)


def normalize_01(y: Iterable[float]) -> Tuple[List[float], NormStats]:
    """Normalise a sequence to the ``[0, 1]`` range."""
    y_list = [float(v) for v in y]
    xmin = min(y_list)
    xmax = max(y_list)
    denom = max(xmax - xmin, 1e-12)
    y_norm = [(v - xmin) / denom for v in y_list]
    return y_norm, NormStats(xmin, xmax)


def denormalize_01(y_norm: Iterable[float], stats: NormStats) -> List[float]:
    """Undo :func:`normalize_01` given :class:`NormStats`."""
    return [v * (stats.xmax - stats.xmin) + stats.xmin for v in y_norm]


def downsample_uniform(x: Sequence, y: Sequence, factor: int) -> Tuple[List, List]:
    """Down–sample ``x``/``y`` by an integer ``factor``."""
    if factor < 2:
        raise ValueError("factor must be >= 2")
    return list(x)[::factor], list(y)[::factor]


def make_windows_1d(seq: Sequence[float], win: int) -> List[List[List[float]]]:
    """Construct a sliding window view of a 1‑D sequence."""
    if win % 2 != 1:
        raise ValueError("Window size must be odd")
    seq = list(seq)
    pad = win // 2
    padded = [seq[0]] * pad + seq + [seq[-1]] * pad
    windows = [padded[i : i + win] for i in range(len(seq))]
    return [[[v] for v in w] for w in windows]


def compute_metrics(y_true: Sequence[float], y_pred: Sequence[float]) -> Dict[str, float]:
    """
    Compute evaluation metrics for super-resolution results.
    
    Args:
        y_true: Ground truth values
        y_pred: Predicted values
    
    Returns:
        Dictionary containing RMSE, MAE, R², PCC, and PSNR
    """
    y_true = list(y_true)
    y_pred = list(y_pred)
    
    if len(y_true) != len(y_pred):
        raise ValueError("y_true and y_pred must have same length")
    
    n = len(y_true)
    if n == 0:
        return {"RMSE": 0.0, "MAE": 0.0, "R2": 0.0, "PCC": 0.0, "PSNR": 0.0}
    
    # Mean squared error and root mean squared error
    mse = sum((yt - yp)**2 for yt, yp in zip(y_true, y_pred)) / n
    rmse = math.sqrt(mse)
    
    # Mean absolute error
    mae = sum(abs(yt - yp) for yt, yp in zip(y_true, y_pred)) / n
    
    # R-squared
    y_mean = sum(y_true) / n
    ss_tot = sum((yt - y_mean)**2 for yt in y_true)
    ss_res = sum((yt - yp)**2 for yt, yp in zip(y_true, y_pred))
    r2 = 1.0 - (ss_res / max(ss_tot, 1e-12))
    
    # Pearson correlation coefficient
    y_true_mean = sum(y_true) / n
    y_pred_mean = sum(y_pred) / n
    
    numerator = sum((yt - y_true_mean) * (yp - y_pred_mean) for yt, yp in zip(y_true, y_pred))
    
    sum_sq_true = sum((yt - y_true_mean)**2 for yt in y_true)
    sum_sq_pred = sum((yp - y_pred_mean)**2 for yp in y_pred)
    denominator = math.sqrt(sum_sq_true * sum_sq_pred)
    
    pcc = numerator / max(denominator, 1e-12)
    
    # PSNR (assuming normalized data in [0,1] range)
    max_value = 1.0
    psnr = 20 * math.log10(max_value / max(math.sqrt(mse), 1e-12))
    
    return {
        "RMSE": rmse,
        "MAE": mae,
        "R2": r2,
        "PCC": pcc,
        "PSNR": psnr
    }


def fft_magnitude(y: Sequence[float]) -> List[float]:
    """
    Compute FFT magnitude spectrum (simplified implementation).
    
    Args:
        y: Input signal
    
    Returns:
        Magnitude spectrum
    """
    y = list(y)
    n = len(y)
    
    # Remove DC component
    y_mean = sum(y) / len(y)
    y = [val - y_mean for val in y]
    
    # Simple DFT implementation for demonstration
    # In practice, would use efficient FFT
    magnitudes = []
    
    for k in range(n // 2 + 1):  # Only positive frequencies
        real_part = sum(y[j] * math.cos(-2 * math.pi * k * j / n) for j in range(n))
        imag_part = sum(y[j] * math.sin(-2 * math.pi * k * j / n) for j in range(n))
        magnitude = math.sqrt(real_part**2 + imag_part**2)
        magnitudes.append(magnitude)
    
    return magnitudes


def linear_interpolation(x_lr: Sequence[float], y_lr: Sequence[float], 
                        x_hr: Sequence[float]) -> List[float]:
    """
    Simple linear interpolation baseline.
    
    Args:
        x_lr: Low-resolution x coordinates
        y_lr: Low-resolution y values
        x_hr: High-resolution x coordinates for interpolation
    
    Returns:
        Interpolated values at x_hr positions
    """
    x_lr = list(x_lr)
    y_lr = list(y_lr)
    x_hr = list(x_hr)
    
    if len(x_lr) != len(y_lr):
        raise ValueError("x_lr and y_lr must have same length")
    
    result = []
    
    for x in x_hr:
        # Find bracketing points
        if x <= x_lr[0]:
            result.append(y_lr[0])
        elif x >= x_lr[-1]:
            result.append(y_lr[-1])
        else:
            # Find interval
            for i in range(len(x_lr) - 1):
                if x_lr[i] <= x <= x_lr[i + 1]:
                    # Linear interpolation
                    t = (x - x_lr[i]) / max(x_lr[i + 1] - x_lr[i], 1e-12)
                    y = y_lr[i] + t * (y_lr[i + 1] - y_lr[i])
                    result.append(y)
                    break
    
    return result

