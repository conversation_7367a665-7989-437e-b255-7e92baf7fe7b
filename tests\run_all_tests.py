#!/usr/bin/env python3
"""
Main test runner for the log fractal interpolation package.

This script runs all tests in sequence and provides a comprehensive
validation of the entire system as described in Guide.md.
"""

import sys
import time
from pathlib import Path

# Make package importable without installation
sys.path.append(str(Path(__file__).resolve().parents[1] / "src"))


def run_test_module(module_name: str) -> bool:
    """
    Run a test module and return success status.
    
    Args:
        module_name: Name of the test module to run
    
    Returns:
        True if all tests passed, False otherwise
    """
    print(f"\n{'='*20} Running {module_name} {'='*20}")
    
    try:
        # Import and run the test module
        module = __import__(module_name)
        
        # Get all test functions
        test_functions = [getattr(module, name) for name in dir(module) 
                         if name.startswith('test_') and callable(getattr(module, name))]
        
        if not test_functions:
            print(f"No test functions found in {module_name}")
            return True
        
        # Run each test function
        passed = 0
        failed = 0
        
        for test_func in test_functions:
            try:
                print(f"  Running {test_func.__name__}...", end=' ')
                test_func()
                print("✓ PASSED")
                passed += 1
            except Exception as e:
                print(f"❌ FAILED: {e}")
                failed += 1
        
        print(f"\n{module_name} Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except ImportError as e:
        print(f"Failed to import {module_name}: {e}")
        return False
    except Exception as e:
        print(f"Error running {module_name}: {e}")
        return False


def main():
    """Main test runner."""
    print("🚀 Starting comprehensive test suite for Log Fractal Interpolation")
    print("📋 This validates the complete workflow described in Guide.md")
    print("-" * 80)
    
    start_time = time.time()
    
    # List of test modules to run
    test_modules = [
        'test_utils',
        'test_fif', 
        'test_ampso',
        'test_bilstm',
        'test_comprehensive'
    ]
    
    results = {}
    total_passed = 0
    total_failed = 0
    
    # Run each test module
    for module in test_modules:
        success = run_test_module(module)
        results[module] = success
        if success:
            total_passed += 1
        else:
            total_failed += 1
    
    # Summary
    elapsed = time.time() - start_time
    print("\n" + "="*80)
    print("🏁 TEST SUITE COMPLETE")
    print("="*80)
    print(f"⏱️  Total time: {elapsed:.2f} seconds")
    print(f"📊 Results: {total_passed} modules passed, {total_failed} modules failed")
    
    print("\n📋 Detailed Results:")
    for module, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {module:20} {status}")
    
    if total_failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("🔬 The fractal interpolation system is working correctly.")
        print("📚 Implementation matches the specifications in Guide.md.")
        
        print("\n🔧 Key Features Validated:")
        print("  ✓ Fractal Interpolation Functions (FIF)")
        print("  ✓ Hurst exponent estimation via R/S analysis")
        print("  ✓ Adaptive Mutation Particle Swarm Optimization (AMPSO)")
        print("  ✓ Bi-directional LSTM refinement")
        print("  ✓ End-to-end super-resolution workflow")
        print("  ✓ Inference mode (without HR ground truth)")
        print("  ✓ Evaluation metrics and baseline comparisons")
        
        return 0
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("🔍 Please check the error messages above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
